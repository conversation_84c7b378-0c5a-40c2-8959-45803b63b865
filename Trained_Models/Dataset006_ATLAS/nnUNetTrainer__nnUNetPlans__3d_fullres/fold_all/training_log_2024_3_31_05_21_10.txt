
#######################################################################
Please cite the following paper when using nnU-Net:
<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>. <PERSON>, <PERSON>, S. A<PERSON>, <PERSON>, <PERSON>, & <PERSON>, K. H. (2021). nnU-Net: a self-configuring method for deep learning-based biomedical image segmentation. Nature methods, 18(2), 203-211.
#######################################################################
 

This is the configuration used by this training:
Configuration name: 3d_fullres
 {'data_identifier': 'nnUNetPlans_3d_fullres', 'preprocessor_name': 'DefaultPreprocessor', 'batch_size': 2, 'patch_size': [128, 160, 112], 'median_image_size_in_voxels': [155.0, 182.0, 145.0], 'spacing': [1.0, 1.0, 1.0], 'normalization_schemes': ['ZScoreNormalization'], 'use_mask_for_norm': [True], 'resampling_fn_data': 'resample_data_or_seg_to_shape', 'resampling_fn_seg': 'resample_data_or_seg_to_shape', 'resampling_fn_data_kwargs': {'is_seg': False, 'order': 3, 'order_z': 0, 'force_separate_z': None}, 'resampling_fn_seg_kwargs': {'is_seg': True, 'order': 1, 'order_z': 0, 'force_separate_z': None}, 'resampling_fn_probabilities': 'resample_data_or_seg_to_shape', 'resampling_fn_probabilities_kwargs': {'is_seg': False, 'order': 1, 'order_z': 0, 'force_separate_z': None}, 'architecture': {'network_class_name': 'dynamic_network_architectures.architectures.unet.PlainConvUNet', 'arch_kwargs': {'n_stages': 6, 'features_per_stage': [32, 64, 128, 256, 320, 320], 'conv_op': 'torch.nn.modules.conv.Conv3d', 'kernel_sizes': [[3, 3, 3], [3, 3, 3], [3, 3, 3], [3, 3, 3], [3, 3, 3], [3, 3, 3]], 'strides': [[1, 1, 1], [2, 2, 2], [2, 2, 2], [2, 2, 2], [2, 2, 2], [2, 2, 1]], 'n_conv_per_stage': [2, 2, 2, 2, 2, 2], 'n_conv_per_stage_decoder': [2, 2, 2, 2, 2], 'conv_bias': True, 'norm_op': 'torch.nn.modules.instancenorm.InstanceNorm3d', 'norm_op_kwargs': {'eps': 1e-05, 'affine': True}, 'dropout_op': None, 'dropout_op_kwargs': None, 'nonlin': 'torch.nn.LeakyReLU', 'nonlin_kwargs': {'inplace': True}, 'deep_supervision': True}, '_kw_requires_import': ['conv_op', 'norm_op', 'dropout_op', 'nonlin']}, 'batch_dice': False} 
 
These are the global plan.json settings:
 {'dataset_name': 'Dataset006_ATLAS', 'plans_name': 'nnUNetPlans', 'original_median_spacing_after_transp': [1.0, 1.0, 1.0], 'original_median_shape_after_transp': [155, 182, 145], 'image_reader_writer': 'SimpleITKIO', 'transpose_forward': [0, 1, 2], 'transpose_backward': [0, 1, 2], 'experiment_planner_used': 'ExperimentPlanner', 'label_manager': 'LabelManager', 'foreground_intensity_properties_per_channel': {'0': {'max': 394.0965881347656, 'mean': 54.31780242919922, 'median': 45.62371063232422, 'min': 0.0, 'percentile_00_5': 0.0, 'percentile_99_5': 247.48562622070312, 'std': 40.10658645629883}}} 
 
2024-03-31 05:21:15.868511: unpacking dataset... 
2024-03-31 05:21:29.259371: unpacking done... 
2024-03-31 05:21:29.260148: do_dummy_2d_data_aug: False 
2024-03-31 05:21:29.322740: Unable to plot network architecture: 
2024-03-31 05:21:29.322792: No module named 'hiddenlayer' 
2024-03-31 05:21:29.425940:  
2024-03-31 05:21:29.426119: Epoch 650 
2024-03-31 05:21:29.426396: Current learning rate: 0.00389 
2024-03-31 05:23:37.852224: train_loss -0.6315 
2024-03-31 05:23:37.852524: val_loss -0.6234 
2024-03-31 05:23:37.852601: Pseudo dice [0.8459] 
2024-03-31 05:23:37.852682: Epoch time: 128.43 s 
2024-03-31 05:23:39.990416:  
2024-03-31 05:23:39.990533: Epoch 651 
2024-03-31 05:23:39.990624: Current learning rate: 0.00388 
2024-03-31 05:25:16.756516: train_loss -0.6437 
2024-03-31 05:25:16.876043: val_loss -0.6317 
2024-03-31 05:25:16.876194: Pseudo dice [0.8303] 
2024-03-31 05:25:16.876286: Epoch time: 96.77 s 
2024-03-31 05:25:18.367969:  
2024-03-31 05:25:18.368248: Epoch 652 
2024-03-31 05:25:18.368387: Current learning rate: 0.00387 
2024-03-31 05:26:51.735470: train_loss -0.6514 
2024-03-31 05:26:51.786112: val_loss -0.6517 
2024-03-31 05:26:51.786257: Pseudo dice [0.8137] 
2024-03-31 05:26:51.786328: Epoch time: 93.37 s 
2024-03-31 05:26:53.338064:  
2024-03-31 05:26:53.338267: Epoch 653 
2024-03-31 05:26:53.338343: Current learning rate: 0.00386 
2024-03-31 05:28:30.026896: train_loss -0.6431 
2024-03-31 05:28:30.051461: val_loss -0.6822 
2024-03-31 05:28:30.051619: Pseudo dice [0.8667] 
2024-03-31 05:28:30.051702: Epoch time: 96.69 s 
2024-03-31 05:28:31.891613:  
2024-03-31 05:28:31.891827: Epoch 654 
2024-03-31 05:28:31.891924: Current learning rate: 0.00385 
2024-03-31 05:30:03.814299: train_loss -0.6548 
2024-03-31 05:30:03.814940: val_loss -0.6649 
2024-03-31 05:30:03.815045: Pseudo dice [0.8554] 
2024-03-31 05:30:03.815157: Epoch time: 91.92 s 
2024-03-31 05:30:03.815244: Yayy! New best EMA pseudo Dice: 0.8418 
2024-03-31 05:30:07.020133:  
2024-03-31 05:30:07.020351: Epoch 655 
2024-03-31 05:30:07.020451: Current learning rate: 0.00384 
2024-03-31 05:31:37.762975: train_loss -0.6481 
2024-03-31 05:31:37.764596: val_loss -0.6403 
2024-03-31 05:31:37.764730: Pseudo dice [0.8109] 
2024-03-31 05:31:37.764810: Epoch time: 90.74 s 
2024-03-31 05:31:39.191130:  
2024-03-31 05:31:39.191323: Epoch 656 
2024-03-31 05:31:39.191423: Current learning rate: 0.00383 
2024-03-31 05:33:15.215003: train_loss -0.6444 
2024-03-31 05:33:15.231010: val_loss -0.6606 
2024-03-31 05:33:15.231191: Pseudo dice [0.8348] 
2024-03-31 05:33:15.231316: Epoch time: 96.02 s 
2024-03-31 05:33:16.640887:  
2024-03-31 05:33:16.641198: Epoch 657 
2024-03-31 05:33:16.641406: Current learning rate: 0.00382 
2024-03-31 05:34:49.419665: train_loss -0.661 
2024-03-31 05:34:49.429981: val_loss -0.6431 
2024-03-31 05:34:49.430397: Pseudo dice [0.8411] 
2024-03-31 05:34:49.430663: Epoch time: 92.78 s 
2024-03-31 05:34:51.497677:  
2024-03-31 05:34:51.498069: Epoch 658 
2024-03-31 05:34:51.498330: Current learning rate: 0.00381 
2024-03-31 05:36:21.498592: train_loss -0.6418 
2024-03-31 05:36:21.500498: val_loss -0.6354 
2024-03-31 05:36:21.500643: Pseudo dice [0.8244] 
2024-03-31 05:36:21.500788: Epoch time: 90.0 s 
2024-03-31 05:36:23.129971:  
2024-03-31 05:36:23.130194: Epoch 659 
2024-03-31 05:36:23.130278: Current learning rate: 0.0038 
2024-03-31 05:37:55.918604: train_loss -0.6298 
2024-03-31 05:37:55.927420: val_loss -0.6154 
2024-03-31 05:37:55.927495: Pseudo dice [0.8383] 
2024-03-31 05:37:55.927564: Epoch time: 92.79 s 
2024-03-31 05:37:57.318586:  
2024-03-31 05:37:57.318794: Epoch 660 
2024-03-31 05:37:57.318885: Current learning rate: 0.00379 
2024-03-31 05:39:24.297715: train_loss -0.6352 
2024-03-31 05:39:24.298344: val_loss -0.6699 
2024-03-31 05:39:24.298430: Pseudo dice [0.8337] 
2024-03-31 05:39:24.298510: Epoch time: 86.98 s 
2024-03-31 05:39:25.912028:  
2024-03-31 05:39:25.912227: Epoch 661 
2024-03-31 05:39:25.912395: Current learning rate: 0.00378 
2024-03-31 05:40:55.491714: train_loss -0.6344 
2024-03-31 05:40:55.508509: val_loss -0.6781 
2024-03-31 05:40:55.508683: Pseudo dice [0.8301] 
2024-03-31 05:40:55.508777: Epoch time: 89.58 s 
2024-03-31 05:40:58.595553:  
2024-03-31 05:40:58.595745: Epoch 662 
2024-03-31 05:40:58.595884: Current learning rate: 0.00377 
2024-03-31 05:42:26.372420: train_loss -0.6397 
2024-03-31 05:42:26.390650: val_loss -0.66 
2024-03-31 05:42:26.390859: Pseudo dice [0.832] 
2024-03-31 05:42:26.391025: Epoch time: 87.78 s 
2024-03-31 05:42:27.921405:  
2024-03-31 05:42:27.921530: Epoch 663 
2024-03-31 05:42:27.921612: Current learning rate: 0.00376 
2024-03-31 05:43:54.993657: train_loss -0.6302 
2024-03-31 05:43:54.994103: val_loss -0.6107 
2024-03-31 05:43:54.994177: Pseudo dice [0.797] 
2024-03-31 05:43:54.994249: Epoch time: 87.07 s 
2024-03-31 05:43:56.352919:  
2024-03-31 05:43:56.353046: Epoch 664 
2024-03-31 05:43:56.353141: Current learning rate: 0.00375 
2024-03-31 05:45:25.962184: train_loss -0.6542 
2024-03-31 05:45:25.971554: val_loss -0.6664 
2024-03-31 05:45:25.971699: Pseudo dice [0.8419] 
2024-03-31 05:45:25.971797: Epoch time: 89.61 s 
2024-03-31 05:45:27.687327:  
2024-03-31 05:45:27.687495: Epoch 665 
2024-03-31 05:45:27.687609: Current learning rate: 0.00374 
2024-03-31 05:47:00.769661: train_loss -0.6534 
2024-03-31 05:47:00.770323: val_loss -0.6676 
2024-03-31 05:47:00.770432: Pseudo dice [0.8489] 
2024-03-31 05:47:00.770549: Epoch time: 93.08 s 
2024-03-31 05:47:02.266868:  
2024-03-31 05:47:02.267041: Epoch 666 
2024-03-31 05:47:02.267148: Current learning rate: 0.00373 
2024-03-31 05:48:41.290485: train_loss -0.6368 
2024-03-31 05:48:41.316825: val_loss -0.6751 
2024-03-31 05:48:41.316995: Pseudo dice [0.8428] 
2024-03-31 05:48:41.317133: Epoch time: 99.02 s 
2024-03-31 05:48:42.867278:  
2024-03-31 05:48:42.867563: Epoch 667 
2024-03-31 05:48:42.867785: Current learning rate: 0.00372 
2024-03-31 05:50:17.100760: train_loss -0.6469 
2024-03-31 05:50:17.124160: val_loss -0.6411 
2024-03-31 05:50:17.124283: Pseudo dice [0.8388] 
2024-03-31 05:50:17.124352: Epoch time: 94.23 s 
2024-03-31 05:50:20.291252:  
2024-03-31 05:50:20.291370: Epoch 668 
2024-03-31 05:50:20.291442: Current learning rate: 0.00371 
2024-03-31 05:51:50.490714: train_loss -0.6355 
2024-03-31 05:51:50.491507: val_loss -0.6552 
2024-03-31 05:51:50.491612: Pseudo dice [0.8607] 
2024-03-31 05:51:50.491754: Epoch time: 90.2 s 
2024-03-31 05:51:51.777138:  
2024-03-31 05:51:51.777416: Epoch 669 
2024-03-31 05:51:51.777570: Current learning rate: 0.0037 
2024-03-31 05:53:23.235976: train_loss -0.6422 
2024-03-31 05:53:23.245968: val_loss -0.6727 
2024-03-31 05:53:23.246094: Pseudo dice [0.861] 
2024-03-31 05:53:23.246172: Epoch time: 91.46 s 
2024-03-31 05:53:25.101743:  
2024-03-31 05:53:25.101884: Epoch 670 
2024-03-31 05:53:25.101969: Current learning rate: 0.00369 
2024-03-31 05:54:53.471061: train_loss -0.6576 
2024-03-31 05:54:53.555112: val_loss -0.6739 
2024-03-31 05:54:53.555251: Pseudo dice [0.8387] 
2024-03-31 05:54:53.555369: Epoch time: 88.37 s 
2024-03-31 05:54:55.308176:  
2024-03-31 05:54:55.308321: Epoch 671 
2024-03-31 05:54:55.308405: Current learning rate: 0.00368 
2024-03-31 05:56:24.817387: train_loss -0.6735 
2024-03-31 05:56:25.129264: val_loss -0.6898 
2024-03-31 05:56:25.129439: Pseudo dice [0.8578] 
2024-03-31 05:56:25.129534: Epoch time: 89.51 s 
2024-03-31 05:56:25.129609: Yayy! New best EMA pseudo Dice: 0.8421 
2024-03-31 05:56:27.829951:  
2024-03-31 05:56:27.830068: Epoch 672 
2024-03-31 05:56:27.830146: Current learning rate: 0.00367 
2024-03-31 05:57:58.921894: train_loss -0.6538 
2024-03-31 05:57:58.994976: val_loss -0.6512 
2024-03-31 05:57:58.995082: Pseudo dice [0.8445] 
2024-03-31 05:57:58.995179: Epoch time: 91.09 s 
2024-03-31 05:57:58.995224: Yayy! New best EMA pseudo Dice: 0.8423 
2024-03-31 05:58:01.571449:  
2024-03-31 05:58:01.571629: Epoch 673 
2024-03-31 05:58:01.571722: Current learning rate: 0.00366 
2024-03-31 05:59:32.904651: train_loss -0.6373 
2024-03-31 05:59:32.905118: val_loss -0.6888 
2024-03-31 05:59:32.905195: Pseudo dice [0.8425] 
2024-03-31 05:59:32.905254: Epoch time: 91.33 s 
2024-03-31 05:59:32.905292: Yayy! New best EMA pseudo Dice: 0.8423 
2024-03-31 05:59:37.210963:  
2024-03-31 05:59:37.211183: Epoch 674 
2024-03-31 05:59:37.211288: Current learning rate: 0.00365 
2024-03-31 06:01:12.950467: train_loss -0.6521 
2024-03-31 06:01:12.951710: val_loss -0.6719 
2024-03-31 06:01:12.951953: Pseudo dice [0.8419] 
2024-03-31 06:01:12.952180: Epoch time: 95.74 s 
2024-03-31 06:01:15.394295:  
2024-03-31 06:01:15.394483: Epoch 675 
2024-03-31 06:01:15.394594: Current learning rate: 0.00364 
2024-03-31 06:03:01.398004: train_loss -0.6445 
2024-03-31 06:03:01.436414: val_loss -0.7016 
2024-03-31 06:03:01.436579: Pseudo dice [0.8522] 
2024-03-31 06:03:01.436666: Epoch time: 106.01 s 
2024-03-31 06:03:01.436728: Yayy! New best EMA pseudo Dice: 0.8433 
2024-03-31 06:03:04.191668:  
2024-03-31 06:03:04.191796: Epoch 676 
2024-03-31 06:03:04.191868: Current learning rate: 0.00363 
2024-03-31 06:04:38.298990: train_loss -0.666 
2024-03-31 06:04:38.299417: val_loss -0.6765 
2024-03-31 06:04:38.299480: Pseudo dice [0.8521] 
2024-03-31 06:04:38.299555: Epoch time: 94.11 s 
2024-03-31 06:04:38.299598: Yayy! New best EMA pseudo Dice: 0.8442 
2024-03-31 06:04:40.676657:  
2024-03-31 06:04:40.676841: Epoch 677 
2024-03-31 06:04:40.676933: Current learning rate: 0.00362 
2024-03-31 06:06:11.064498: train_loss -0.6514 
2024-03-31 06:06:11.065091: val_loss -0.6748 
2024-03-31 06:06:11.065167: Pseudo dice [0.8691] 
2024-03-31 06:06:11.065238: Epoch time: 90.39 s 
2024-03-31 06:06:11.065286: Yayy! New best EMA pseudo Dice: 0.8467 
2024-03-31 06:06:14.196849:  
2024-03-31 06:06:14.197049: Epoch 678 
2024-03-31 06:06:14.197183: Current learning rate: 0.00361 
2024-03-31 06:07:56.149976: train_loss -0.6534 
2024-03-31 06:07:56.350307: val_loss -0.6227 
2024-03-31 06:07:56.350510: Pseudo dice [0.8288] 
2024-03-31 06:07:56.350951: Epoch time: 101.95 s 
2024-03-31 06:07:58.269902:  
2024-03-31 06:07:58.270612: Epoch 679 
2024-03-31 06:07:58.270713: Current learning rate: 0.0036 
2024-03-31 06:09:29.937207: train_loss -0.6502 
2024-03-31 06:09:29.966329: val_loss -0.6316 
2024-03-31 06:09:29.966484: Pseudo dice [0.8578] 
2024-03-31 06:09:29.966572: Epoch time: 91.67 s 
2024-03-31 06:09:33.179387:  
2024-03-31 06:09:33.179567: Epoch 680 
2024-03-31 06:09:33.179710: Current learning rate: 0.00359 
2024-03-31 06:11:03.684018: train_loss -0.6471 
2024-03-31 06:11:03.684543: val_loss -0.6803 
2024-03-31 06:11:03.684709: Pseudo dice [0.8504] 
2024-03-31 06:11:03.684858: Epoch time: 90.51 s 
2024-03-31 06:11:05.678162:  
2024-03-31 06:11:05.678325: Epoch 681 
2024-03-31 06:11:05.678406: Current learning rate: 0.00358 
2024-03-31 06:12:36.115936: train_loss -0.6318 
2024-03-31 06:12:36.135879: val_loss -0.6546 
2024-03-31 06:12:36.136020: Pseudo dice [0.8452] 
2024-03-31 06:12:36.136108: Epoch time: 90.44 s 
2024-03-31 06:12:38.277579:  
2024-03-31 06:12:38.278008: Epoch 682 
2024-03-31 06:12:38.278357: Current learning rate: 0.00357 
2024-03-31 06:14:14.192144: train_loss -0.6327 
2024-03-31 06:14:14.231626: val_loss -0.6833 
2024-03-31 06:14:14.231724: Pseudo dice [0.858] 
2024-03-31 06:14:14.231799: Epoch time: 95.92 s 
2024-03-31 06:14:14.231844: Yayy! New best EMA pseudo Dice: 0.8476 
2024-03-31 06:14:17.515021:  
2024-03-31 06:14:17.515144: Epoch 683 
2024-03-31 06:14:17.515231: Current learning rate: 0.00356 
2024-03-31 06:15:47.414162: train_loss -0.6467 
2024-03-31 06:15:47.414605: val_loss -0.6772 
2024-03-31 06:15:47.414703: Pseudo dice [0.8515] 
2024-03-31 06:15:47.414780: Epoch time: 89.9 s 
2024-03-31 06:15:47.414834: Yayy! New best EMA pseudo Dice: 0.848 
2024-03-31 06:15:50.441163:  
2024-03-31 06:15:50.441397: Epoch 684 
2024-03-31 06:15:50.441584: Current learning rate: 0.00355 
2024-03-31 06:17:24.674090: train_loss -0.6493 
2024-03-31 06:17:24.674909: val_loss -0.6718 
2024-03-31 06:17:24.674988: Pseudo dice [0.856] 
2024-03-31 06:17:24.675062: Epoch time: 94.23 s 
2024-03-31 06:17:24.675110: Yayy! New best EMA pseudo Dice: 0.8488 
2024-03-31 06:17:28.893078:  
2024-03-31 06:17:28.893236: Epoch 685 
2024-03-31 06:17:28.893319: Current learning rate: 0.00354 
2024-03-31 06:19:05.765551: train_loss -0.6475 
2024-03-31 06:19:05.795692: val_loss -0.6786 
2024-03-31 06:19:05.795911: Pseudo dice [0.8592] 
2024-03-31 06:19:05.796031: Epoch time: 96.87 s 
2024-03-31 06:19:05.796111: Yayy! New best EMA pseudo Dice: 0.8498 
2024-03-31 06:19:08.649889:  
2024-03-31 06:19:08.693883: Epoch 686 
2024-03-31 06:19:08.694072: Current learning rate: 0.00353 
2024-03-31 06:20:41.758893: train_loss -0.6379 
2024-03-31 06:20:41.771442: val_loss -0.6037 
2024-03-31 06:20:41.771704: Pseudo dice [0.8628] 
2024-03-31 06:20:41.771808: Epoch time: 93.11 s 
2024-03-31 06:20:41.771880: Yayy! New best EMA pseudo Dice: 0.8511 
2024-03-31 06:20:44.701886:  
2024-03-31 06:20:44.702003: Epoch 687 
2024-03-31 06:20:44.702085: Current learning rate: 0.00352 
2024-03-31 06:22:24.856380: train_loss -0.6305 
2024-03-31 06:22:24.879539: val_loss -0.6639 
2024-03-31 06:22:24.879900: Pseudo dice [0.844] 
2024-03-31 06:22:24.880355: Epoch time: 100.16 s 
2024-03-31 06:22:26.479599:  
2024-03-31 06:22:26.479777: Epoch 688 
2024-03-31 06:22:26.479869: Current learning rate: 0.00351 
2024-03-31 06:24:12.865170: train_loss -0.6728 
2024-03-31 06:24:12.943204: val_loss -0.6909 
2024-03-31 06:24:12.943448: Pseudo dice [0.8675] 
2024-03-31 06:24:12.943626: Epoch time: 106.39 s 
2024-03-31 06:24:12.943805: Yayy! New best EMA pseudo Dice: 0.8521 
2024-03-31 06:24:15.848673:  
2024-03-31 06:24:15.849070: Epoch 689 
2024-03-31 06:24:15.849270: Current learning rate: 0.0035 
2024-03-31 06:26:02.376774: train_loss -0.6213 
2024-03-31 06:26:02.395129: val_loss -0.6852 
2024-03-31 06:26:02.395275: Pseudo dice [0.8493] 
2024-03-31 06:26:02.395360: Epoch time: 106.53 s 
2024-03-31 06:26:05.119860:  
2024-03-31 06:26:05.120146: Epoch 690 
2024-03-31 06:26:05.120328: Current learning rate: 0.00349 
2024-03-31 06:27:54.155110: train_loss -0.6462 
2024-03-31 06:27:54.161267: val_loss -0.6499 
2024-03-31 06:27:54.161372: Pseudo dice [0.8482] 
2024-03-31 06:27:54.161458: Epoch time: 109.04 s 
2024-03-31 06:27:57.390729:  
2024-03-31 06:27:57.390866: Epoch 691 
2024-03-31 06:27:57.390952: Current learning rate: 0.00348 
2024-03-31 06:29:42.768199: train_loss -0.6506 
2024-03-31 06:29:42.794337: val_loss -0.6783 
2024-03-31 06:29:42.794672: Pseudo dice [0.8575] 
2024-03-31 06:29:42.794931: Epoch time: 105.38 s 
2024-03-31 06:29:44.666997:  
2024-03-31 06:29:44.667330: Epoch 692 
2024-03-31 06:29:44.667482: Current learning rate: 0.00346 
2024-03-31 06:31:30.035825: train_loss -0.6448 
2024-03-31 06:31:30.047738: val_loss -0.6836 
2024-03-31 06:31:30.047972: Pseudo dice [0.843] 
2024-03-31 06:31:30.048212: Epoch time: 105.37 s 
2024-03-31 06:31:31.826170:  
2024-03-31 06:31:31.826312: Epoch 693 
2024-03-31 06:31:31.826408: Current learning rate: 0.00345 
2024-03-31 06:33:05.310407: train_loss -0.6447 
2024-03-31 06:33:05.363566: val_loss -0.6303 
2024-03-31 06:33:05.363697: Pseudo dice [0.8489] 
2024-03-31 06:33:05.363781: Epoch time: 93.49 s 
2024-03-31 06:33:07.649931:  
2024-03-31 06:33:07.650133: Epoch 694 
2024-03-31 06:33:07.650247: Current learning rate: 0.00344 
2024-03-31 06:34:51.656547: train_loss -0.6561 
2024-03-31 06:34:51.657052: val_loss -0.6797 
2024-03-31 06:34:51.657166: Pseudo dice [0.8416] 
2024-03-31 06:34:51.657241: Epoch time: 104.01 s 
2024-03-31 06:34:53.673689:  
2024-03-31 06:34:53.674039: Epoch 695 
2024-03-31 06:34:53.674177: Current learning rate: 0.00343 
2024-03-31 06:36:43.341038: train_loss -0.6596 
2024-03-31 06:36:43.361819: val_loss -0.6274 
2024-03-31 06:36:43.362027: Pseudo dice [0.8477] 
2024-03-31 06:36:43.362139: Epoch time: 109.67 s 
2024-03-31 06:36:46.119881:  
2024-03-31 06:36:46.120113: Epoch 696 
2024-03-31 06:36:46.120286: Current learning rate: 0.00342 
2024-03-31 06:38:27.296214: train_loss -0.6654 
2024-03-31 06:38:27.375045: val_loss -0.6492 
2024-03-31 06:38:27.375202: Pseudo dice [0.8264] 
2024-03-31 06:38:27.375342: Epoch time: 101.18 s 
2024-03-31 06:38:30.023636:  
2024-03-31 06:38:30.024192: Epoch 697 
2024-03-31 06:38:30.024350: Current learning rate: 0.00341 
2024-03-31 06:40:12.659539: train_loss -0.6527 
2024-03-31 06:40:12.701665: val_loss -0.6962 
2024-03-31 06:40:12.701808: Pseudo dice [0.842] 
2024-03-31 06:40:12.701883: Epoch time: 102.64 s 
2024-03-31 06:40:15.859055:  
2024-03-31 06:40:15.859425: Epoch 698 
2024-03-31 06:40:15.859649: Current learning rate: 0.0034 
2024-03-31 06:42:07.468540: train_loss -0.6629 
2024-03-31 06:42:07.507813: val_loss -0.6552 
2024-03-31 06:42:07.507946: Pseudo dice [0.8361] 
2024-03-31 06:42:07.508029: Epoch time: 111.61 s 
2024-03-31 06:42:09.371207:  
2024-03-31 06:42:09.371468: Epoch 699 
2024-03-31 06:42:09.371600: Current learning rate: 0.00339 
2024-03-31 06:43:40.593727: train_loss -0.6534 
2024-03-31 06:43:40.594440: val_loss -0.6854 
2024-03-31 06:43:40.594986: Pseudo dice [0.8501] 
2024-03-31 06:43:40.595076: Epoch time: 91.22 s 
2024-03-31 06:43:43.237426:  
2024-03-31 06:43:43.237602: Epoch 700 
2024-03-31 06:43:43.237692: Current learning rate: 0.00338 
2024-03-31 06:45:14.197104: train_loss -0.6627 
2024-03-31 06:45:14.197900: val_loss -0.6591 
2024-03-31 06:45:14.197993: Pseudo dice [0.8665] 
2024-03-31 06:45:14.198075: Epoch time: 90.96 s 
2024-03-31 06:45:15.534178:  
2024-03-31 06:45:15.534299: Epoch 701 
2024-03-31 06:45:15.534375: Current learning rate: 0.00337 
2024-03-31 06:46:48.250670: train_loss -0.6436 
2024-03-31 06:46:48.281767: val_loss -0.6856 
2024-03-31 06:46:48.281906: Pseudo dice [0.8392] 
2024-03-31 06:46:48.281992: Epoch time: 92.72 s 
2024-03-31 06:46:50.448006:  
2024-03-31 06:46:50.448139: Epoch 702 
2024-03-31 06:46:50.448223: Current learning rate: 0.00336 
2024-03-31 06:48:36.373191: train_loss -0.6448 
2024-03-31 06:48:36.400178: val_loss -0.6658 
2024-03-31 06:48:36.400319: Pseudo dice [0.8476] 
2024-03-31 06:48:36.400418: Epoch time: 105.93 s 
2024-03-31 06:48:38.918121:  
2024-03-31 06:48:38.918265: Epoch 703 
2024-03-31 06:48:38.918354: Current learning rate: 0.00335 
2024-03-31 06:50:24.254198: train_loss -0.639 
2024-03-31 06:50:24.280814: val_loss -0.6819 
2024-03-31 06:50:24.280953: Pseudo dice [0.87] 
2024-03-31 06:50:24.281041: Epoch time: 105.34 s 
2024-03-31 06:50:27.504667:  
2024-03-31 06:50:27.504869: Epoch 704 
2024-03-31 06:50:27.504986: Current learning rate: 0.00334 
2024-03-31 06:52:11.210586: train_loss -0.6484 
2024-03-31 06:52:11.214248: val_loss -0.6755 
2024-03-31 06:52:11.214381: Pseudo dice [0.8658] 
2024-03-31 06:52:11.214480: Epoch time: 103.71 s 
2024-03-31 06:52:13.176987:  
2024-03-31 06:52:13.177212: Epoch 705 
2024-03-31 06:52:13.177350: Current learning rate: 0.00333 
2024-03-31 06:53:54.301364: train_loss -0.6323 
2024-03-31 06:53:54.325269: val_loss -0.6766 
2024-03-31 06:53:54.325468: Pseudo dice [0.8354] 
2024-03-31 06:53:54.325572: Epoch time: 101.13 s 
2024-03-31 06:53:56.603503:  
2024-03-31 06:53:56.603751: Epoch 706 
2024-03-31 06:53:56.603878: Current learning rate: 0.00332 
2024-03-31 06:55:41.207060: train_loss -0.6445 
2024-03-31 06:55:41.238383: val_loss -0.647 
2024-03-31 06:55:41.238514: Pseudo dice [0.8375] 
2024-03-31 06:55:41.238595: Epoch time: 104.61 s 
2024-03-31 06:55:43.430531:  
2024-03-31 06:55:43.430697: Epoch 707 
2024-03-31 06:55:43.430800: Current learning rate: 0.00331 
2024-03-31 06:57:22.328888: train_loss -0.6156 
2024-03-31 06:57:22.361837: val_loss -0.6528 
2024-03-31 06:57:22.362046: Pseudo dice [0.8169] 
2024-03-31 06:57:22.362191: Epoch time: 98.9 s 
2024-03-31 06:57:24.312890:  
2024-03-31 06:57:24.313036: Epoch 708 
2024-03-31 06:57:24.313126: Current learning rate: 0.0033 
2024-03-31 06:59:11.034226: train_loss -0.6533 
2024-03-31 06:59:11.053802: val_loss -0.676 
2024-03-31 06:59:11.053924: Pseudo dice [0.869] 
2024-03-31 06:59:11.054012: Epoch time: 106.72 s 
2024-03-31 06:59:13.153431:  
2024-03-31 06:59:13.153829: Epoch 709 
2024-03-31 06:59:13.153947: Current learning rate: 0.00329 
2024-03-31 07:00:56.909863: train_loss -0.6481 
2024-03-31 07:00:56.935526: val_loss -0.6476 
2024-03-31 07:00:56.935658: Pseudo dice [0.8274] 
2024-03-31 07:00:56.935746: Epoch time: 103.76 s 
2024-03-31 07:00:59.932506:  
2024-03-31 07:00:59.932653: Epoch 710 
2024-03-31 07:00:59.932738: Current learning rate: 0.00328 
2024-03-31 07:02:47.348646: train_loss -0.6484 
2024-03-31 07:02:47.460680: val_loss -0.6777 
2024-03-31 07:02:47.460838: Pseudo dice [0.8613] 
2024-03-31 07:02:47.460909: Epoch time: 107.42 s 
2024-03-31 07:02:49.166883:  
2024-03-31 07:02:49.166998: Epoch 711 
2024-03-31 07:02:49.167070: Current learning rate: 0.00327 
2024-03-31 07:04:31.348247: train_loss -0.6423 
2024-03-31 07:04:31.409752: val_loss -0.6199 
2024-03-31 07:04:31.409894: Pseudo dice [0.8357] 
2024-03-31 07:04:31.409980: Epoch time: 102.18 s 
2024-03-31 07:04:33.284880:  
2024-03-31 07:04:33.285023: Epoch 712 
2024-03-31 07:04:33.285120: Current learning rate: 0.00326 
2024-03-31 07:06:20.117933: train_loss -0.6363 
2024-03-31 07:06:20.159061: val_loss -0.6398 
2024-03-31 07:06:20.159265: Pseudo dice [0.8127] 
2024-03-31 07:06:20.159438: Epoch time: 106.83 s 
2024-03-31 07:06:22.139858:  
2024-03-31 07:06:22.140343: Epoch 713 
2024-03-31 07:06:22.140440: Current learning rate: 0.00325 
2024-03-31 07:08:02.169969: train_loss -0.6451 
2024-03-31 07:08:02.183579: val_loss -0.6229 
2024-03-31 07:08:02.183767: Pseudo dice [0.8103] 
2024-03-31 07:08:02.183895: Epoch time: 100.03 s 
2024-03-31 07:08:03.883130:  
2024-03-31 07:08:03.883234: Epoch 714 
2024-03-31 07:08:03.883315: Current learning rate: 0.00324 
2024-03-31 07:09:41.643034: train_loss -0.6288 
2024-03-31 07:09:41.649236: val_loss -0.673 
2024-03-31 07:09:41.650002: Pseudo dice [0.8676] 
2024-03-31 07:09:41.650393: Epoch time: 97.76 s 
2024-03-31 07:09:44.053223:  
2024-03-31 07:09:44.053429: Epoch 715 
2024-03-31 07:09:44.053536: Current learning rate: 0.00323 
2024-03-31 07:11:31.472556: train_loss -0.6306 
2024-03-31 07:11:31.498932: val_loss -0.6593 
2024-03-31 07:11:31.499057: Pseudo dice [0.8523] 
2024-03-31 07:11:31.499220: Epoch time: 107.42 s 
2024-03-31 07:11:34.555291:  
2024-03-31 07:11:34.555418: Epoch 716 
2024-03-31 07:11:34.555502: Current learning rate: 0.00322 
2024-03-31 07:13:24.842251: train_loss -0.6353 
2024-03-31 07:13:24.842731: val_loss -0.5975 
2024-03-31 07:13:24.842825: Pseudo dice [0.7987] 
2024-03-31 07:13:24.842929: Epoch time: 110.29 s 
2024-03-31 07:13:26.667927:  
2024-03-31 07:13:26.668430: Epoch 717 
2024-03-31 07:13:26.668586: Current learning rate: 0.00321 
2024-03-31 07:15:18.192276: train_loss -0.6367 
2024-03-31 07:15:18.206241: val_loss -0.6773 
2024-03-31 07:15:18.206503: Pseudo dice [0.8178] 
2024-03-31 07:15:18.206597: Epoch time: 111.53 s 
2024-03-31 07:15:20.628241:  
2024-03-31 07:15:20.628442: Epoch 718 
2024-03-31 07:15:20.628564: Current learning rate: 0.0032 
2024-03-31 07:17:06.178124: train_loss -0.6421 
2024-03-31 07:17:06.178688: val_loss -0.6823 
2024-03-31 07:17:06.178772: Pseudo dice [0.8639] 
2024-03-31 07:17:06.178868: Epoch time: 105.55 s 
2024-03-31 07:17:08.695642:  
2024-03-31 07:17:08.695820: Epoch 719 
2024-03-31 07:17:08.695922: Current learning rate: 0.00319 
2024-03-31 07:18:53.248491: train_loss -0.6488 
2024-03-31 07:18:53.288818: val_loss -0.6605 
2024-03-31 07:18:53.288997: Pseudo dice [0.8463] 
2024-03-31 07:18:53.289287: Epoch time: 104.55 s 
2024-03-31 07:18:54.994781:  
2024-03-31 07:18:54.994907: Epoch 720 
2024-03-31 07:18:54.994994: Current learning rate: 0.00318 
2024-03-31 07:20:35.596068: train_loss -0.6502 
2024-03-31 07:20:35.663096: val_loss -0.6751 
2024-03-31 07:20:35.663243: Pseudo dice [0.8335] 
2024-03-31 07:20:35.663338: Epoch time: 100.6 s 
2024-03-31 07:20:37.352138:  
2024-03-31 07:20:37.368435: Epoch 721 
2024-03-31 07:20:37.368557: Current learning rate: 0.00317 
2024-03-31 07:22:28.963280: train_loss -0.6596 
2024-03-31 07:22:28.979049: val_loss -0.6581 
2024-03-31 07:22:28.979206: Pseudo dice [0.8249] 
2024-03-31 07:22:28.979357: Epoch time: 111.61 s 
2024-03-31 07:22:32.159623:  
2024-03-31 07:22:32.159987: Epoch 722 
2024-03-31 07:22:32.160114: Current learning rate: 0.00316 
2024-03-31 07:24:18.161521: train_loss -0.6459 
2024-03-31 07:24:18.195337: val_loss -0.6748 
2024-03-31 07:24:18.195522: Pseudo dice [0.8317] 
2024-03-31 07:24:18.195651: Epoch time: 106.0 s 
2024-03-31 07:24:20.009645:  
2024-03-31 07:24:20.009981: Epoch 723 
2024-03-31 07:24:20.010113: Current learning rate: 0.00315 
2024-03-31 07:26:01.795778: train_loss -0.6464 
2024-03-31 07:26:01.796528: val_loss -0.6614 
2024-03-31 07:26:01.796653: Pseudo dice [0.8346] 
2024-03-31 07:26:01.796743: Epoch time: 101.79 s 
2024-03-31 07:26:03.445416:  
2024-03-31 07:26:03.445586: Epoch 724 
2024-03-31 07:26:03.445673: Current learning rate: 0.00314 
2024-03-31 07:27:49.804808: train_loss -0.6432 
2024-03-31 07:27:49.805465: val_loss -0.6953 
2024-03-31 07:27:49.805563: Pseudo dice [0.8763] 
2024-03-31 07:27:49.805654: Epoch time: 106.36 s 
2024-03-31 07:27:52.612371:  
2024-03-31 07:27:52.612657: Epoch 725 
2024-03-31 07:27:52.612814: Current learning rate: 0.00313 
2024-03-31 07:29:33.560635: train_loss -0.6339 
2024-03-31 07:29:33.586474: val_loss -0.637 
2024-03-31 07:29:33.586715: Pseudo dice [0.8652] 
2024-03-31 07:29:33.586908: Epoch time: 100.95 s 
2024-03-31 07:29:35.166343:  
2024-03-31 07:29:35.166498: Epoch 726 
2024-03-31 07:29:35.166578: Current learning rate: 0.00312 
2024-03-31 07:31:19.701002: train_loss -0.6349 
2024-03-31 07:31:19.721262: val_loss -0.6454 
2024-03-31 07:31:19.721392: Pseudo dice [0.834] 
2024-03-31 07:31:19.721470: Epoch time: 104.54 s 
2024-03-31 07:31:22.325723:  
2024-03-31 07:31:22.325888: Epoch 727 
2024-03-31 07:31:22.325994: Current learning rate: 0.00311 
2024-03-31 07:33:07.422137: train_loss -0.624 
2024-03-31 07:33:07.423849: val_loss -0.6819 
2024-03-31 07:33:07.423951: Pseudo dice [0.8375] 
2024-03-31 07:33:07.424027: Epoch time: 105.1 s 
2024-03-31 07:33:10.328236:  
2024-03-31 07:33:10.328665: Epoch 728 
2024-03-31 07:33:10.328916: Current learning rate: 0.0031 
2024-03-31 07:34:51.560244: train_loss -0.6609 
2024-03-31 07:34:51.561110: val_loss -0.6635 
2024-03-31 07:34:51.561205: Pseudo dice [0.8407] 
2024-03-31 07:34:51.561296: Epoch time: 101.23 s 
2024-03-31 07:34:53.477133:  
2024-03-31 07:34:53.477249: Epoch 729 
2024-03-31 07:34:53.477333: Current learning rate: 0.00309 
2024-03-31 07:36:39.160804: train_loss -0.6658 
2024-03-31 07:36:39.254461: val_loss -0.6683 
2024-03-31 07:36:39.254754: Pseudo dice [0.8443] 
2024-03-31 07:36:39.254916: Epoch time: 105.68 s 
2024-03-31 07:36:41.094057:  
2024-03-31 07:36:41.094232: Epoch 730 
2024-03-31 07:36:41.094376: Current learning rate: 0.00308 
2024-03-31 07:38:26.922276: train_loss -0.6496 
2024-03-31 07:38:27.002856: val_loss -0.6661 
2024-03-31 07:38:27.003001: Pseudo dice [0.8211] 
2024-03-31 07:38:27.003083: Epoch time: 105.83 s 
2024-03-31 07:38:29.023125:  
2024-03-31 07:38:29.023432: Epoch 731 
2024-03-31 07:38:29.023530: Current learning rate: 0.00307 
2024-03-31 07:40:07.917297: train_loss -0.664 
2024-03-31 07:40:07.968178: val_loss -0.6815 
2024-03-31 07:40:07.968327: Pseudo dice [0.8539] 
2024-03-31 07:40:07.968411: Epoch time: 98.9 s 
2024-03-31 07:40:09.629498:  
2024-03-31 07:40:09.629616: Epoch 732 
2024-03-31 07:40:09.629703: Current learning rate: 0.00306 
2024-03-31 07:41:46.798768: train_loss -0.6332 
2024-03-31 07:41:46.822781: val_loss -0.6946 
2024-03-31 07:41:46.822914: Pseudo dice [0.8453] 
2024-03-31 07:41:46.823007: Epoch time: 97.17 s 
2024-03-31 07:41:48.852679:  
2024-03-31 07:41:48.852868: Epoch 733 
2024-03-31 07:41:48.853027: Current learning rate: 0.00305 
2024-03-31 07:43:29.500273: train_loss -0.6588 
2024-03-31 07:43:29.715510: val_loss -0.6492 
2024-03-31 07:43:29.715673: Pseudo dice [0.8203] 
2024-03-31 07:43:29.715764: Epoch time: 100.65 s 
2024-03-31 07:43:32.088364:  
2024-03-31 07:43:32.088486: Epoch 734 
2024-03-31 07:43:32.088567: Current learning rate: 0.00304 
2024-03-31 07:45:17.199409: train_loss -0.645 
2024-03-31 07:45:17.214061: val_loss -0.6719 
2024-03-31 07:45:17.214246: Pseudo dice [0.812] 
2024-03-31 07:45:17.214377: Epoch time: 105.11 s 
2024-03-31 07:45:20.947021:  
2024-03-31 07:45:20.947547: Epoch 735 
2024-03-31 07:45:20.947818: Current learning rate: 0.00303 
2024-03-31 07:47:05.739363: train_loss -0.6425 
2024-03-31 07:47:05.762551: val_loss -0.6595 
2024-03-31 07:47:05.762676: Pseudo dice [0.8497] 
2024-03-31 07:47:05.762764: Epoch time: 104.79 s 
2024-03-31 07:47:08.774995:  
2024-03-31 07:47:08.775604: Epoch 736 
2024-03-31 07:47:08.775781: Current learning rate: 0.00302 
2024-03-31 07:48:50.092666: train_loss -0.6576 
2024-03-31 07:48:50.120895: val_loss -0.6812 
2024-03-31 07:48:50.121011: Pseudo dice [0.8427] 
2024-03-31 07:48:50.121100: Epoch time: 101.32 s 
2024-03-31 07:48:52.229149:  
2024-03-31 07:48:52.229306: Epoch 737 
2024-03-31 07:48:52.229426: Current learning rate: 0.00301 
2024-03-31 07:50:38.950344: train_loss -0.656 
2024-03-31 07:50:38.962341: val_loss -0.662 
2024-03-31 07:50:38.962603: Pseudo dice [0.8602] 
2024-03-31 07:50:38.962809: Epoch time: 106.72 s 
2024-03-31 07:50:41.104572:  
2024-03-31 07:50:41.104720: Epoch 738 
2024-03-31 07:50:41.104805: Current learning rate: 0.003 
2024-03-31 07:52:18.029821: train_loss -0.656 
2024-03-31 07:52:18.058517: val_loss -0.6658 
2024-03-31 07:52:18.058734: Pseudo dice [0.8445] 
2024-03-31 07:52:18.058804: Epoch time: 96.93 s 
2024-03-31 07:52:19.569125:  
2024-03-31 07:52:19.569262: Epoch 739 
2024-03-31 07:52:19.569355: Current learning rate: 0.00299 
2024-03-31 07:54:06.934079: train_loss -0.6389 
2024-03-31 07:54:07.007451: val_loss -0.6478 
2024-03-31 07:54:07.007635: Pseudo dice [0.8419] 
2024-03-31 07:54:07.007743: Epoch time: 107.37 s 
2024-03-31 07:54:09.807120:  
2024-03-31 07:54:09.807389: Epoch 740 
2024-03-31 07:54:09.807530: Current learning rate: 0.00297 
2024-03-31 07:55:58.378220: train_loss -0.6481 
2024-03-31 07:55:58.393738: val_loss -0.6684 
2024-03-31 07:55:58.393877: Pseudo dice [0.8385] 
2024-03-31 07:55:58.393964: Epoch time: 108.57 s 
2024-03-31 07:56:01.736730:  
2024-03-31 07:56:01.736945: Epoch 741 
2024-03-31 07:56:01.737042: Current learning rate: 0.00296 
2024-03-31 07:57:49.419335: train_loss -0.6531 
2024-03-31 07:57:49.441252: val_loss -0.6164 
2024-03-31 07:57:49.441399: Pseudo dice [0.8414] 
2024-03-31 07:57:49.441489: Epoch time: 107.68 s 
2024-03-31 07:57:51.915859:  
2024-03-31 07:57:51.916097: Epoch 742 
2024-03-31 07:57:51.916213: Current learning rate: 0.00295 
2024-03-31 07:59:40.796894: train_loss -0.6674 
2024-03-31 07:59:40.810312: val_loss -0.647 
2024-03-31 07:59:40.810593: Pseudo dice [0.8129] 
2024-03-31 07:59:40.810769: Epoch time: 108.88 s 
2024-03-31 07:59:42.537955:  
2024-03-31 07:59:42.538080: Epoch 743 
2024-03-31 07:59:42.538169: Current learning rate: 0.00294 
2024-03-31 08:01:25.343911: train_loss -0.6568 
2024-03-31 08:01:25.358782: val_loss -0.6648 
2024-03-31 08:01:25.358964: Pseudo dice [0.8436] 
2024-03-31 08:01:25.359056: Epoch time: 102.81 s 
2024-03-31 08:01:27.910867:  
2024-03-31 08:01:27.911150: Epoch 744 
2024-03-31 08:01:27.911414: Current learning rate: 0.00293 
2024-03-31 08:03:09.875698: train_loss -0.6753 
2024-03-31 08:03:09.940716: val_loss -0.7014 
2024-03-31 08:03:09.940833: Pseudo dice [0.8398] 
2024-03-31 08:03:09.940904: Epoch time: 101.97 s 
2024-03-31 08:03:11.791927:  
2024-03-31 08:03:11.792044: Epoch 745 
2024-03-31 08:03:11.792127: Current learning rate: 0.00292 
2024-03-31 08:04:55.509801: train_loss -0.672 
2024-03-31 08:04:55.510314: val_loss -0.6923 
2024-03-31 08:04:55.510402: Pseudo dice [0.8746] 
2024-03-31 08:04:55.510478: Epoch time: 103.72 s 
2024-03-31 08:04:57.251651:  
2024-03-31 08:04:57.251766: Epoch 746 
2024-03-31 08:04:57.251865: Current learning rate: 0.00291 
2024-03-31 08:06:45.040877: train_loss -0.6465 
2024-03-31 08:06:45.045979: val_loss -0.6849 
2024-03-31 08:06:45.046093: Pseudo dice [0.846] 
2024-03-31 08:06:45.046188: Epoch time: 107.79 s 
2024-03-31 08:06:47.059928:  
2024-03-31 08:06:47.060315: Epoch 747 
2024-03-31 08:06:47.060417: Current learning rate: 0.0029 
2024-03-31 08:08:32.598760: train_loss -0.653 
2024-03-31 08:08:32.669557: val_loss -0.7197 
2024-03-31 08:08:32.669712: Pseudo dice [0.8708] 
2024-03-31 08:08:32.669811: Epoch time: 105.54 s 
2024-03-31 08:08:36.450976:  
2024-03-31 08:08:36.451137: Epoch 748 
2024-03-31 08:08:36.451228: Current learning rate: 0.00289 
2024-03-31 08:10:19.057734: train_loss -0.6542 
2024-03-31 08:10:19.081415: val_loss -0.6533 
2024-03-31 08:10:19.081596: Pseudo dice [0.8435] 
2024-03-31 08:10:19.081741: Epoch time: 102.61 s 
2024-03-31 08:10:20.832115:  
2024-03-31 08:10:20.832664: Epoch 749 
2024-03-31 08:10:20.833039: Current learning rate: 0.00288 
2024-03-31 08:12:03.933464: train_loss -0.6568 
2024-03-31 08:12:03.966783: val_loss -0.6816 
2024-03-31 08:12:03.967006: Pseudo dice [0.8505] 
2024-03-31 08:12:03.967132: Epoch time: 103.1 s 
2024-03-31 08:12:07.469798:  
2024-03-31 08:12:07.470153: Epoch 750 
2024-03-31 08:12:07.470258: Current learning rate: 0.00287 
2024-03-31 08:13:44.757666: train_loss -0.6499 
2024-03-31 08:13:44.780188: val_loss -0.6825 
2024-03-31 08:13:44.780313: Pseudo dice [0.8262] 
2024-03-31 08:13:44.780448: Epoch time: 97.29 s 
2024-03-31 08:13:46.965658:  
2024-03-31 08:13:46.965824: Epoch 751 
2024-03-31 08:13:46.965921: Current learning rate: 0.00286 
2024-03-31 08:15:28.412018: train_loss -0.6793 
2024-03-31 08:15:28.488587: val_loss -0.673 
2024-03-31 08:15:28.488720: Pseudo dice [0.8763] 
2024-03-31 08:15:28.488799: Epoch time: 101.45 s 
2024-03-31 08:15:30.282403:  
2024-03-31 08:15:30.282665: Epoch 752 
2024-03-31 08:15:30.282768: Current learning rate: 0.00285 
2024-03-31 08:17:18.119002: train_loss -0.6648 
2024-03-31 08:17:18.119486: val_loss -0.6268 
2024-03-31 08:17:18.119557: Pseudo dice [0.8472] 
2024-03-31 08:17:18.119613: Epoch time: 107.84 s 
2024-03-31 08:17:19.878136:  
2024-03-31 08:17:19.878279: Epoch 753 
2024-03-31 08:17:19.878380: Current learning rate: 0.00284 
2024-03-31 08:19:05.852273: train_loss -0.679 
2024-03-31 08:19:05.852820: val_loss -0.6898 
2024-03-31 08:19:05.852911: Pseudo dice [0.8504] 
2024-03-31 08:19:05.852994: Epoch time: 105.98 s 
2024-03-31 08:19:09.446597:  
2024-03-31 08:19:09.446973: Epoch 754 
2024-03-31 08:19:09.447070: Current learning rate: 0.00283 
2024-03-31 08:21:01.234733: train_loss -0.6693 
2024-03-31 08:21:01.276245: val_loss -0.6462 
2024-03-31 08:21:01.276393: Pseudo dice [0.8563] 
2024-03-31 08:21:01.276483: Epoch time: 111.79 s 
2024-03-31 08:21:03.518695:  
2024-03-31 08:21:03.518850: Epoch 755 
2024-03-31 08:21:03.518941: Current learning rate: 0.00282 
2024-03-31 08:22:47.021655: train_loss -0.6516 
2024-03-31 08:22:47.142283: val_loss -0.6915 
2024-03-31 08:22:47.142431: Pseudo dice [0.8459] 
2024-03-31 08:22:47.142515: Epoch time: 103.5 s 
2024-03-31 08:22:49.046267:  
2024-03-31 08:22:49.046500: Epoch 756 
2024-03-31 08:22:49.046603: Current learning rate: 0.00281 
2024-03-31 08:24:30.484221: train_loss -0.6658 
2024-03-31 08:24:30.499841: val_loss -0.6933 
2024-03-31 08:24:30.499950: Pseudo dice [0.8489] 
2024-03-31 08:24:30.500015: Epoch time: 101.44 s 
2024-03-31 08:24:31.983274:  
2024-03-31 08:24:31.983482: Epoch 757 
2024-03-31 08:24:31.983621: Current learning rate: 0.0028 
2024-03-31 08:26:08.924753: train_loss -0.6563 
2024-03-31 08:26:08.957584: val_loss -0.7224 
2024-03-31 08:26:08.957700: Pseudo dice [0.8685] 
2024-03-31 08:26:08.957769: Epoch time: 96.94 s 
2024-03-31 08:26:10.681621:  
2024-03-31 08:26:10.681835: Epoch 758 
2024-03-31 08:26:10.681993: Current learning rate: 0.00279 
2024-03-31 08:27:53.215464: train_loss -0.6698 
2024-03-31 08:27:53.215887: val_loss -0.6789 
2024-03-31 08:27:53.218782: Pseudo dice [0.8669] 
2024-03-31 08:27:53.218900: Epoch time: 102.54 s 
2024-03-31 08:27:55.068342:  
2024-03-31 08:27:55.068486: Epoch 759 
2024-03-31 08:27:55.068613: Current learning rate: 0.00278 
2024-03-31 08:29:34.530883: train_loss -0.6674 
2024-03-31 08:29:34.613199: val_loss -0.6632 
2024-03-31 08:29:34.613347: Pseudo dice [0.853] 
2024-03-31 08:29:34.613442: Epoch time: 99.46 s 
2024-03-31 08:29:37.913669:  
2024-03-31 08:29:37.914070: Epoch 760 
2024-03-31 08:29:37.914198: Current learning rate: 0.00277 
2024-03-31 08:31:19.923320: train_loss -0.6526 
2024-03-31 08:31:19.966785: val_loss -0.6625 
2024-03-31 08:31:19.966898: Pseudo dice [0.8464] 
2024-03-31 08:31:19.967017: Epoch time: 102.01 s 
2024-03-31 08:31:21.414037:  
2024-03-31 08:31:21.414234: Epoch 761 
2024-03-31 08:31:21.414333: Current learning rate: 0.00276 
2024-03-31 08:33:02.800422: train_loss -0.6645 
2024-03-31 08:33:02.846425: val_loss -0.6892 
2024-03-31 08:33:02.846558: Pseudo dice [0.8714] 
2024-03-31 08:33:02.846684: Epoch time: 101.39 s 
2024-03-31 08:33:02.846730: Yayy! New best EMA pseudo Dice: 0.8534 
2024-03-31 08:33:05.540209:  
2024-03-31 08:33:05.540369: Epoch 762 
2024-03-31 08:33:05.540451: Current learning rate: 0.00275 
2024-03-31 08:34:47.684067: train_loss -0.6733 
2024-03-31 08:34:47.698914: val_loss -0.6518 
2024-03-31 08:34:47.699054: Pseudo dice [0.8364] 
2024-03-31 08:34:47.699138: Epoch time: 102.14 s 
2024-03-31 08:34:49.626168:  
2024-03-31 08:34:49.626675: Epoch 763 
2024-03-31 08:34:49.626815: Current learning rate: 0.00274 
2024-03-31 08:36:22.308300: train_loss -0.6592 
2024-03-31 08:36:22.315660: val_loss -0.6658 
2024-03-31 08:36:22.315885: Pseudo dice [0.8437] 
2024-03-31 08:36:22.316035: Epoch time: 92.68 s 
2024-03-31 08:36:24.762688:  
2024-03-31 08:36:24.762886: Epoch 764 
2024-03-31 08:36:24.763017: Current learning rate: 0.00273 
2024-03-31 08:38:11.024805: train_loss -0.657 
2024-03-31 08:38:11.026741: val_loss -0.7035 
2024-03-31 08:38:11.026890: Pseudo dice [0.8494] 
2024-03-31 08:38:11.027045: Epoch time: 106.26 s 
2024-03-31 08:38:12.854468:  
2024-03-31 08:38:12.854585: Epoch 765 
2024-03-31 08:38:12.854666: Current learning rate: 0.00272 
2024-03-31 08:40:03.258180: train_loss -0.6671 
2024-03-31 08:40:03.327015: val_loss -0.6761 
2024-03-31 08:40:03.327137: Pseudo dice [0.8598] 
2024-03-31 08:40:03.327240: Epoch time: 110.4 s 
2024-03-31 08:40:06.513618:  
2024-03-31 08:40:06.513751: Epoch 766 
2024-03-31 08:40:06.513844: Current learning rate: 0.00271 
2024-03-31 08:41:54.769875: train_loss -0.6779 
2024-03-31 08:41:54.794321: val_loss -0.691 
2024-03-31 08:41:54.794634: Pseudo dice [0.8747] 
2024-03-31 08:41:54.794870: Epoch time: 108.26 s 
2024-03-31 08:41:54.794968: Yayy! New best EMA pseudo Dice: 0.854 
2024-03-31 08:41:57.978233:  
2024-03-31 08:41:57.978497: Epoch 767 
2024-03-31 08:41:57.978632: Current learning rate: 0.0027 
2024-03-31 08:43:36.756186: train_loss -0.6806 
2024-03-31 08:43:36.771610: val_loss -0.6745 
2024-03-31 08:43:36.771783: Pseudo dice [0.8492] 
2024-03-31 08:43:36.771960: Epoch time: 98.78 s 
2024-03-31 08:43:38.745380:  
2024-03-31 08:43:38.745852: Epoch 768 
2024-03-31 08:43:38.746002: Current learning rate: 0.00268 
2024-03-31 08:45:25.243866: train_loss -0.6759 
2024-03-31 08:45:25.263373: val_loss -0.7018 
2024-03-31 08:45:25.263517: Pseudo dice [0.8498] 
2024-03-31 08:45:25.263602: Epoch time: 106.5 s 
2024-03-31 08:45:27.416567:  
2024-03-31 08:45:27.416837: Epoch 769 
2024-03-31 08:45:27.417004: Current learning rate: 0.00267 
2024-03-31 08:47:10.067627: train_loss -0.6711 
2024-03-31 08:47:10.088116: val_loss -0.7155 
2024-03-31 08:47:10.088264: Pseudo dice [0.8776] 
2024-03-31 08:47:10.088412: Epoch time: 102.65 s 
2024-03-31 08:47:10.088477: Yayy! New best EMA pseudo Dice: 0.8556 
2024-03-31 08:47:12.967552:  
2024-03-31 08:47:12.967709: Epoch 770 
2024-03-31 08:47:12.967856: Current learning rate: 0.00266 
2024-03-31 08:48:58.401326: train_loss -0.6392 
2024-03-31 08:48:58.401914: val_loss -0.6559 
2024-03-31 08:48:58.402007: Pseudo dice [0.8308] 
2024-03-31 08:48:58.402088: Epoch time: 105.43 s 
2024-03-31 08:49:00.776279:  
2024-03-31 08:49:00.776484: Epoch 771 
2024-03-31 08:49:00.776670: Current learning rate: 0.00265 
2024-03-31 08:50:47.296227: train_loss -0.6597 
2024-03-31 08:50:47.351746: val_loss -0.6995 
2024-03-31 08:50:47.351894: Pseudo dice [0.8803] 
2024-03-31 08:50:47.351968: Epoch time: 106.52 s 
2024-03-31 08:50:47.352032: Yayy! New best EMA pseudo Dice: 0.8558 
2024-03-31 08:50:51.834907:  
2024-03-31 08:50:51.835071: Epoch 772 
2024-03-31 08:50:51.835169: Current learning rate: 0.00264 
2024-03-31 08:52:40.140317: train_loss -0.6423 
2024-03-31 08:52:40.185549: val_loss -0.6514 
2024-03-31 08:52:40.185690: Pseudo dice [0.839] 
2024-03-31 08:52:40.185796: Epoch time: 108.31 s 
2024-03-31 08:52:42.507976:  
2024-03-31 08:52:42.508107: Epoch 773 
2024-03-31 08:52:42.508197: Current learning rate: 0.00263 
2024-03-31 08:54:21.315173: train_loss -0.633 
2024-03-31 08:54:21.360152: val_loss -0.6715 
2024-03-31 08:54:21.360300: Pseudo dice [0.849] 
2024-03-31 08:54:21.360414: Epoch time: 98.81 s 
2024-03-31 08:54:23.122522:  
2024-03-31 08:54:23.122644: Epoch 774 
2024-03-31 08:54:23.122733: Current learning rate: 0.00262 
2024-03-31 08:56:06.234691: train_loss -0.6627 
2024-03-31 08:56:06.253856: val_loss -0.6734 
2024-03-31 08:56:06.254090: Pseudo dice [0.8715] 
2024-03-31 08:56:06.254279: Epoch time: 103.11 s 
2024-03-31 08:56:08.620689:  
2024-03-31 08:56:08.620999: Epoch 775 
2024-03-31 08:56:08.621238: Current learning rate: 0.00261 
2024-03-31 08:57:51.869389: train_loss -0.6727 
2024-03-31 08:57:51.870031: val_loss -0.6955 
2024-03-31 08:57:51.870143: Pseudo dice [0.8694] 
2024-03-31 08:57:51.870260: Epoch time: 103.25 s 
2024-03-31 08:57:51.870340: Yayy! New best EMA pseudo Dice: 0.8568 
2024-03-31 08:57:55.039966:  
2024-03-31 08:57:55.040160: Epoch 776 
2024-03-31 08:57:55.040257: Current learning rate: 0.0026 
2024-03-31 08:59:36.466871: train_loss -0.6791 
2024-03-31 08:59:36.495277: val_loss -0.6934 
2024-03-31 08:59:36.495474: Pseudo dice [0.8485] 
2024-03-31 08:59:36.495681: Epoch time: 101.43 s 
2024-03-31 08:59:38.286336:  
2024-03-31 08:59:38.286514: Epoch 777 
2024-03-31 08:59:38.286602: Current learning rate: 0.00259 
2024-03-31 09:01:30.150576: train_loss -0.6614 
2024-03-31 09:01:30.166220: val_loss -0.6608 
2024-03-31 09:01:30.166342: Pseudo dice [0.8349] 
2024-03-31 09:01:30.166431: Epoch time: 111.87 s 
2024-03-31 09:01:33.910754:  
2024-03-31 09:01:33.910915: Epoch 778 
2024-03-31 09:01:33.911015: Current learning rate: 0.00258 
2024-03-31 09:03:29.334966: train_loss -0.6527 
2024-03-31 09:03:29.432991: val_loss -0.6911 
2024-03-31 09:03:29.433172: Pseudo dice [0.8527] 
2024-03-31 09:03:29.433264: Epoch time: 115.43 s 
2024-03-31 09:03:31.517068:  
2024-03-31 09:03:31.517244: Epoch 779 
2024-03-31 09:03:31.517339: Current learning rate: 0.00257 
2024-03-31 09:05:20.179703: train_loss -0.6545 
2024-03-31 09:05:20.206902: val_loss -0.6312 
2024-03-31 09:05:20.207083: Pseudo dice [0.8093] 
2024-03-31 09:05:20.207199: Epoch time: 108.66 s 
2024-03-31 09:05:22.021992:  
2024-03-31 09:05:22.022168: Epoch 780 
2024-03-31 09:05:22.022255: Current learning rate: 0.00256 
2024-03-31 09:07:07.096674: train_loss -0.6584 
2024-03-31 09:07:07.111650: val_loss -0.6721 
2024-03-31 09:07:07.111766: Pseudo dice [0.815] 
2024-03-31 09:07:07.111868: Epoch time: 105.08 s 
2024-03-31 09:07:09.383160:  
2024-03-31 09:07:09.383307: Epoch 781 
2024-03-31 09:07:09.383385: Current learning rate: 0.00255 
2024-03-31 09:08:49.742443: train_loss -0.6662 
2024-03-31 09:08:49.755767: val_loss -0.7015 
2024-03-31 09:08:49.755865: Pseudo dice [0.8558] 
2024-03-31 09:08:49.755928: Epoch time: 100.36 s 
2024-03-31 09:08:51.395205:  
2024-03-31 09:08:51.395352: Epoch 782 
2024-03-31 09:08:51.395432: Current learning rate: 0.00254 
2024-03-31 09:10:28.218971: train_loss -0.6741 
2024-03-31 09:10:28.220081: val_loss -0.6876 
2024-03-31 09:10:28.220167: Pseudo dice [0.862] 
2024-03-31 09:10:28.220240: Epoch time: 96.82 s 
2024-03-31 09:10:29.854532:  
2024-03-31 09:10:29.854746: Epoch 783 
2024-03-31 09:10:29.854838: Current learning rate: 0.00253 
2024-03-31 09:12:21.629671: train_loss -0.6682 
2024-03-31 09:12:21.630197: val_loss -0.6562 
2024-03-31 09:12:21.630288: Pseudo dice [0.8303] 
2024-03-31 09:12:21.630365: Epoch time: 111.78 s 
2024-03-31 09:12:25.702328:  
2024-03-31 09:12:25.702621: Epoch 784 
2024-03-31 09:12:25.702722: Current learning rate: 0.00252 
2024-03-31 09:14:15.879977: train_loss -0.6862 
2024-03-31 09:14:15.945907: val_loss -0.6874 
2024-03-31 09:14:15.946033: Pseudo dice [0.8569] 
2024-03-31 09:14:15.946110: Epoch time: 110.18 s 
2024-03-31 09:14:17.729476:  
2024-03-31 09:14:17.729723: Epoch 785 
2024-03-31 09:14:17.729809: Current learning rate: 0.00251 
2024-03-31 09:16:04.651579: train_loss -0.6635 
2024-03-31 09:16:04.652509: val_loss -0.6705 
2024-03-31 09:16:04.652601: Pseudo dice [0.8693] 
2024-03-31 09:16:04.652664: Epoch time: 106.92 s 
2024-03-31 09:16:06.197375:  
2024-03-31 09:16:06.197544: Epoch 786 
2024-03-31 09:16:06.197686: Current learning rate: 0.0025 
2024-03-31 09:17:49.959031: train_loss -0.6637 
2024-03-31 09:17:49.960134: val_loss -0.6735 
2024-03-31 09:17:49.960195: Pseudo dice [0.838] 
2024-03-31 09:17:49.960257: Epoch time: 103.76 s 
2024-03-31 09:17:52.088681:  
2024-03-31 09:17:52.088889: Epoch 787 
2024-03-31 09:17:52.089052: Current learning rate: 0.00249 
2024-03-31 09:19:40.164048: train_loss -0.6596 
2024-03-31 09:19:40.180513: val_loss -0.6931 
2024-03-31 09:19:40.180681: Pseudo dice [0.8435] 
2024-03-31 09:19:40.180799: Epoch time: 108.08 s 
2024-03-31 09:19:42.511809:  
2024-03-31 09:19:42.557151: Epoch 788 
2024-03-31 09:19:42.557363: Current learning rate: 0.00248 
2024-03-31 09:21:23.373963: train_loss -0.6734 
2024-03-31 09:21:23.432585: val_loss -0.6754 
2024-03-31 09:21:23.432725: Pseudo dice [0.8741] 
2024-03-31 09:21:23.432862: Epoch time: 100.86 s 
2024-03-31 09:21:25.289281:  
2024-03-31 09:21:25.289485: Epoch 789 
2024-03-31 09:21:25.289614: Current learning rate: 0.00247 
2024-03-31 09:23:15.143178: train_loss -0.6509 
2024-03-31 09:23:15.234572: val_loss -0.6751 
2024-03-31 09:23:15.234716: Pseudo dice [0.8582] 
2024-03-31 09:23:15.234807: Epoch time: 109.85 s 
2024-03-31 09:23:18.320755:  
2024-03-31 09:23:18.320908: Epoch 790 
2024-03-31 09:23:18.320993: Current learning rate: 0.00245 
2024-03-31 09:25:07.831667: train_loss -0.6657 
2024-03-31 09:25:07.908181: val_loss -0.7182 
2024-03-31 09:25:07.908318: Pseudo dice [0.8631] 
2024-03-31 09:25:07.908391: Epoch time: 109.51 s 
2024-03-31 09:25:10.999561:  
2024-03-31 09:25:10.999870: Epoch 791 
2024-03-31 09:25:11.000045: Current learning rate: 0.00244 
2024-03-31 09:27:00.623490: train_loss -0.676 
2024-03-31 09:27:00.624099: val_loss -0.7124 
2024-03-31 09:27:00.624164: Pseudo dice [0.8559] 
2024-03-31 09:27:00.624292: Epoch time: 109.62 s 
2024-03-31 09:27:02.252749:  
2024-03-31 09:27:02.253036: Epoch 792 
2024-03-31 09:27:02.253262: Current learning rate: 0.00243 
2024-03-31 09:28:43.736481: train_loss -0.6639 
2024-03-31 09:28:43.788579: val_loss -0.688 
2024-03-31 09:28:43.788719: Pseudo dice [0.8486] 
2024-03-31 09:28:43.788805: Epoch time: 101.48 s 
2024-03-31 09:28:46.030639:  
2024-03-31 09:28:46.030815: Epoch 793 
2024-03-31 09:28:46.030895: Current learning rate: 0.00242 
2024-03-31 09:30:30.929071: train_loss -0.6547 
2024-03-31 09:30:30.929591: val_loss -0.7148 
2024-03-31 09:30:30.929677: Pseudo dice [0.8722] 
2024-03-31 09:30:30.929763: Epoch time: 104.9 s 
2024-03-31 09:30:32.569853:  
2024-03-31 09:30:32.570129: Epoch 794 
2024-03-31 09:30:32.570235: Current learning rate: 0.00241 
2024-03-31 09:32:04.412892: train_loss -0.6766 
2024-03-31 09:32:04.432979: val_loss -0.6864 
2024-03-31 09:32:04.433633: Pseudo dice [0.8638] 
2024-03-31 09:32:04.433777: Epoch time: 91.84 s 
2024-03-31 09:32:06.020450:  
2024-03-31 09:32:06.020705: Epoch 795 
2024-03-31 09:32:06.020797: Current learning rate: 0.0024 
2024-03-31 09:33:49.679168: train_loss -0.6637 
2024-03-31 09:33:49.699198: val_loss -0.7019 
2024-03-31 09:33:49.699383: Pseudo dice [0.867] 
2024-03-31 09:33:49.699491: Epoch time: 103.66 s 
2024-03-31 09:33:52.019051:  
2024-03-31 09:33:52.019344: Epoch 796 
2024-03-31 09:33:52.019543: Current learning rate: 0.00239 
2024-03-31 09:35:40.303483: train_loss -0.6508 
2024-03-31 09:35:40.331908: val_loss -0.6538 
2024-03-31 09:35:40.332021: Pseudo dice [0.8481] 
2024-03-31 09:35:40.332097: Epoch time: 108.29 s 
2024-03-31 09:35:43.576046:  
2024-03-31 09:35:43.576173: Epoch 797 
2024-03-31 09:35:43.576252: Current learning rate: 0.00238 
2024-03-31 09:37:33.410580: train_loss -0.6401 
2024-03-31 09:37:33.498307: val_loss -0.6725 
2024-03-31 09:37:33.498434: Pseudo dice [0.8646] 
2024-03-31 09:37:33.498588: Epoch time: 109.84 s 
2024-03-31 09:37:35.433074:  
2024-03-31 09:37:35.433233: Epoch 798 
2024-03-31 09:37:35.433330: Current learning rate: 0.00237 
2024-03-31 09:39:23.227894: train_loss -0.6598 
2024-03-31 09:39:23.314070: val_loss -0.6995 
2024-03-31 09:39:23.314202: Pseudo dice [0.8769] 
2024-03-31 09:39:23.314314: Epoch time: 107.8 s 
2024-03-31 09:39:23.314388: Yayy! New best EMA pseudo Dice: 0.8586 
2024-03-31 09:39:26.574468:  
2024-03-31 09:39:26.574600: Epoch 799 
2024-03-31 09:39:26.574684: Current learning rate: 0.00236 
2024-03-31 09:41:09.427491: train_loss -0.6548 
2024-03-31 09:41:09.455337: val_loss -0.6932 
2024-03-31 09:41:09.455797: Pseudo dice [0.8589] 
2024-03-31 09:41:09.455954: Epoch time: 102.85 s 
2024-03-31 09:41:11.443774: Yayy! New best EMA pseudo Dice: 0.8587 
2024-03-31 09:41:14.200011:  
2024-03-31 09:41:14.200171: Epoch 800 
2024-03-31 09:41:14.200341: Current learning rate: 0.00235 
2024-03-31 09:42:49.641031: train_loss -0.6765 
2024-03-31 09:42:49.873167: val_loss -0.6976 
2024-03-31 09:42:49.873531: Pseudo dice [0.8664] 
2024-03-31 09:42:49.873713: Epoch time: 95.44 s 
2024-03-31 09:42:49.873834: Yayy! New best EMA pseudo Dice: 0.8594 
2024-03-31 09:42:53.535102:  
2024-03-31 09:42:53.535328: Epoch 801 
2024-03-31 09:42:53.535407: Current learning rate: 0.00234 
2024-03-31 09:44:37.336112: train_loss -0.6744 
2024-03-31 09:44:37.371522: val_loss -0.6995 
2024-03-31 09:44:37.371695: Pseudo dice [0.8588] 
2024-03-31 09:44:37.373300: Epoch time: 103.8 s 
2024-03-31 09:44:39.237726:  
2024-03-31 09:44:39.237972: Epoch 802 
2024-03-31 09:44:39.238073: Current learning rate: 0.00233 
2024-03-31 09:46:30.429235: train_loss -0.6679 
2024-03-31 09:46:30.478850: val_loss -0.6555 
2024-03-31 09:46:30.479003: Pseudo dice [0.879] 
2024-03-31 09:46:30.479147: Epoch time: 111.19 s 
2024-03-31 09:46:30.479277: Yayy! New best EMA pseudo Dice: 0.8613 
2024-03-31 09:46:34.974630:  
2024-03-31 09:46:34.975001: Epoch 803 
2024-03-31 09:46:34.975146: Current learning rate: 0.00232 
2024-03-31 09:48:18.970909: train_loss -0.6813 
2024-03-31 09:48:19.053254: val_loss -0.7093 
2024-03-31 09:48:19.053505: Pseudo dice [0.8494] 
2024-03-31 09:48:19.053755: Epoch time: 104.0 s 
2024-03-31 09:48:21.719640:  
2024-03-31 09:48:21.719989: Epoch 804 
2024-03-31 09:48:21.720154: Current learning rate: 0.00231 
2024-03-31 09:50:10.547583: train_loss -0.6868 
2024-03-31 09:50:10.577466: val_loss -0.7154 
2024-03-31 09:50:10.577620: Pseudo dice [0.868] 
2024-03-31 09:50:10.577764: Epoch time: 108.83 s 
2024-03-31 09:50:12.697571:  
2024-03-31 09:50:12.697841: Epoch 805 
2024-03-31 09:50:12.697939: Current learning rate: 0.0023 
2024-03-31 09:51:56.309772: train_loss -0.6758 
2024-03-31 09:51:56.354975: val_loss -0.6968 
2024-03-31 09:51:56.355145: Pseudo dice [0.8679] 
2024-03-31 09:51:56.355296: Epoch time: 103.61 s 
2024-03-31 09:51:56.355377: Yayy! New best EMA pseudo Dice: 0.8616 
2024-03-31 09:51:59.371596:  
2024-03-31 09:51:59.371749: Epoch 806 
2024-03-31 09:51:59.371834: Current learning rate: 0.00229 
2024-03-31 09:53:37.165454: train_loss -0.6934 
2024-03-31 09:53:37.227170: val_loss -0.7078 
2024-03-31 09:53:37.227255: Pseudo dice [0.8399] 
2024-03-31 09:53:37.227358: Epoch time: 97.79 s 
2024-03-31 09:53:38.779448:  
2024-03-31 09:53:38.779672: Epoch 807 
2024-03-31 09:53:38.779752: Current learning rate: 0.00228 
2024-03-31 09:55:26.271312: train_loss -0.6928 
2024-03-31 09:55:26.292474: val_loss -0.6984 
2024-03-31 09:55:26.292595: Pseudo dice [0.8716] 
2024-03-31 09:55:26.292737: Epoch time: 107.49 s 
2024-03-31 09:55:28.516125:  
2024-03-31 09:55:28.516466: Epoch 808 
2024-03-31 09:55:28.516747: Current learning rate: 0.00226 
2024-03-31 09:57:21.642253: train_loss -0.6908 
2024-03-31 09:57:21.700272: val_loss -0.7037 
2024-03-31 09:57:21.700468: Pseudo dice [0.8464] 
2024-03-31 09:57:21.700572: Epoch time: 113.13 s 
2024-03-31 09:57:25.421237:  
2024-03-31 09:57:25.421418: Epoch 809 
2024-03-31 09:57:25.421552: Current learning rate: 0.00225 
2024-03-31 09:59:18.009355: train_loss -0.6747 
2024-03-31 09:59:18.181983: val_loss -0.6998 
2024-03-31 09:59:18.182111: Pseudo dice [0.853] 
2024-03-31 09:59:18.182208: Epoch time: 112.59 s 
2024-03-31 09:59:20.176926:  
2024-03-31 09:59:20.177057: Epoch 810 
2024-03-31 09:59:20.177154: Current learning rate: 0.00224 
2024-03-31 10:01:09.346302: train_loss -0.6702 
2024-03-31 10:01:09.357473: val_loss -0.714 
2024-03-31 10:01:09.357603: Pseudo dice [0.8859] 
2024-03-31 10:01:09.357689: Epoch time: 109.17 s 
2024-03-31 10:01:10.891038:  
2024-03-31 10:01:10.891346: Epoch 811 
2024-03-31 10:01:10.891535: Current learning rate: 0.00223 
2024-03-31 10:02:57.744599: train_loss -0.6812 
2024-03-31 10:02:57.795900: val_loss -0.6959 
2024-03-31 10:02:57.796093: Pseudo dice [0.8492] 
2024-03-31 10:02:57.796185: Epoch time: 106.85 s 
2024-03-31 10:02:59.765537:  
2024-03-31 10:02:59.765674: Epoch 812 
2024-03-31 10:02:59.765774: Current learning rate: 0.00222 
2024-03-31 10:04:42.821097: train_loss -0.6742 
2024-03-31 10:04:42.902757: val_loss -0.6937 
2024-03-31 10:04:42.903015: Pseudo dice [0.8495] 
2024-03-31 10:04:42.903277: Epoch time: 103.06 s 
2024-03-31 10:04:44.948023:  
2024-03-31 10:04:44.948228: Epoch 813 
2024-03-31 10:04:44.948333: Current learning rate: 0.00221 
2024-03-31 10:06:26.629837: train_loss -0.6829 
2024-03-31 10:06:26.638988: val_loss -0.7087 
2024-03-31 10:06:26.639134: Pseudo dice [0.8648] 
2024-03-31 10:06:26.639252: Epoch time: 101.68 s 
2024-03-31 10:06:28.768695:  
2024-03-31 10:06:28.769145: Epoch 814 
2024-03-31 10:06:28.769374: Current learning rate: 0.0022 
2024-03-31 10:08:19.173423: train_loss -0.6584 
2024-03-31 10:08:19.173977: val_loss -0.7105 
2024-03-31 10:08:19.174045: Pseudo dice [0.8715] 
2024-03-31 10:08:19.174158: Epoch time: 110.41 s 
2024-03-31 10:08:22.105490:  
2024-03-31 10:08:22.105636: Epoch 815 
2024-03-31 10:08:22.105744: Current learning rate: 0.00219 
2024-03-31 10:10:05.988876: train_loss -0.6694 
2024-03-31 10:10:06.021260: val_loss -0.6945 
2024-03-31 10:10:06.021476: Pseudo dice [0.8753] 
2024-03-31 10:10:06.021633: Epoch time: 103.88 s 
2024-03-31 10:10:06.021740: Yayy! New best EMA pseudo Dice: 0.8623 
2024-03-31 10:10:09.211775:  
2024-03-31 10:10:09.211894: Epoch 816 
2024-03-31 10:10:09.211964: Current learning rate: 0.00218 
2024-03-31 10:11:58.912871: train_loss -0.6663 
2024-03-31 10:11:58.986095: val_loss -0.6846 
2024-03-31 10:11:58.986246: Pseudo dice [0.8472] 
2024-03-31 10:11:58.986335: Epoch time: 109.7 s 
2024-03-31 10:12:01.350033:  
2024-03-31 10:12:01.350611: Epoch 817 
2024-03-31 10:12:01.350724: Current learning rate: 0.00217 
2024-03-31 10:13:46.132558: train_loss -0.671 
2024-03-31 10:13:46.170349: val_loss -0.6953 
2024-03-31 10:13:46.170546: Pseudo dice [0.8703] 
2024-03-31 10:13:46.170707: Epoch time: 104.78 s 
2024-03-31 10:13:48.638944:  
2024-03-31 10:13:48.639266: Epoch 818 
2024-03-31 10:13:48.639376: Current learning rate: 0.00216 
2024-03-31 10:15:31.390820: train_loss -0.6892 
2024-03-31 10:15:31.449927: val_loss -0.705 
2024-03-31 10:15:31.450048: Pseudo dice [0.8667] 
2024-03-31 10:15:31.450122: Epoch time: 102.75 s 
2024-03-31 10:15:33.178019:  
2024-03-31 10:15:33.178259: Epoch 819 
2024-03-31 10:15:33.178435: Current learning rate: 0.00215 
2024-03-31 10:17:18.405268: train_loss -0.6617 
2024-03-31 10:17:18.465134: val_loss -0.6983 
2024-03-31 10:17:18.465270: Pseudo dice [0.8588] 
2024-03-31 10:17:18.465354: Epoch time: 105.23 s 
2024-03-31 10:17:20.936111:  
2024-03-31 10:17:20.936245: Epoch 820 
2024-03-31 10:17:20.936327: Current learning rate: 0.00214 
2024-03-31 10:19:12.839331: train_loss -0.678 
2024-03-31 10:19:12.860248: val_loss -0.6652 
2024-03-31 10:19:12.860398: Pseudo dice [0.8499] 
2024-03-31 10:19:12.860501: Epoch time: 111.9 s 
2024-03-31 10:19:14.718714:  
2024-03-31 10:19:14.719017: Epoch 821 
2024-03-31 10:19:14.719286: Current learning rate: 0.00213 
2024-03-31 10:21:03.034584: train_loss -0.6437 
2024-03-31 10:21:03.070425: val_loss -0.683 
2024-03-31 10:21:03.070591: Pseudo dice [0.8397] 
2024-03-31 10:21:03.070677: Epoch time: 108.32 s 
2024-03-31 10:21:06.469489:  
2024-03-31 10:21:06.469819: Epoch 822 
2024-03-31 10:21:06.469955: Current learning rate: 0.00212 
2024-03-31 10:22:55.382483: train_loss -0.6549 
2024-03-31 10:22:55.401363: val_loss -0.6876 
2024-03-31 10:22:55.401552: Pseudo dice [0.8521] 
2024-03-31 10:22:55.401790: Epoch time: 108.91 s 
2024-03-31 10:22:57.521426:  
2024-03-31 10:22:57.521654: Epoch 823 
2024-03-31 10:22:57.521988: Current learning rate: 0.0021 
2024-03-31 10:24:45.787710: train_loss -0.6732 
2024-03-31 10:24:45.831029: val_loss -0.6743 
2024-03-31 10:24:45.831695: Pseudo dice [0.811] 
2024-03-31 10:24:45.832002: Epoch time: 108.27 s 
2024-03-31 10:24:47.963106:  
2024-03-31 10:24:47.963650: Epoch 824 
2024-03-31 10:24:47.964156: Current learning rate: 0.00209 
2024-03-31 10:26:31.115018: train_loss -0.6684 
2024-03-31 10:26:31.133852: val_loss -0.7037 
2024-03-31 10:26:31.134162: Pseudo dice [0.8761] 
2024-03-31 10:26:31.134472: Epoch time: 103.16 s 
2024-03-31 10:26:33.146430:  
2024-03-31 10:26:33.146646: Epoch 825 
2024-03-31 10:26:33.146736: Current learning rate: 0.00208 
2024-03-31 10:28:18.276181: train_loss -0.6778 
2024-03-31 10:28:18.325622: val_loss -0.6873 
2024-03-31 10:28:18.325935: Pseudo dice [0.8373] 
2024-03-31 10:28:18.326092: Epoch time: 105.13 s 
2024-03-31 10:28:20.512393:  
2024-03-31 10:28:20.512615: Epoch 826 
2024-03-31 10:28:20.512743: Current learning rate: 0.00207 
2024-03-31 10:30:08.848422: train_loss -0.6606 
2024-03-31 10:30:08.861952: val_loss -0.6447 
2024-03-31 10:30:08.862207: Pseudo dice [0.8312] 
2024-03-31 10:30:08.862381: Epoch time: 108.34 s 
2024-03-31 10:30:10.350383:  
2024-03-31 10:30:10.351060: Epoch 827 
2024-03-31 10:30:10.351307: Current learning rate: 0.00206 
2024-03-31 10:31:55.228701: train_loss -0.6802 
2024-03-31 10:31:55.264630: val_loss -0.6976 
2024-03-31 10:31:55.264790: Pseudo dice [0.8549] 
2024-03-31 10:31:55.264885: Epoch time: 104.88 s 
2024-03-31 10:31:58.077536:  
2024-03-31 10:31:58.077707: Epoch 828 
2024-03-31 10:31:58.077801: Current learning rate: 0.00205 
2024-03-31 10:33:46.942578: train_loss -0.6774 
2024-03-31 10:33:46.973604: val_loss -0.712 
2024-03-31 10:33:46.973962: Pseudo dice [0.8648] 
2024-03-31 10:33:46.974234: Epoch time: 108.87 s 
2024-03-31 10:33:48.680954:  
2024-03-31 10:33:48.681119: Epoch 829 
2024-03-31 10:33:48.681226: Current learning rate: 0.00204 
2024-03-31 10:35:35.659765: train_loss -0.6685 
2024-03-31 10:35:35.687800: val_loss -0.6943 
2024-03-31 10:35:35.687906: Pseudo dice [0.88] 
2024-03-31 10:35:35.688009: Epoch time: 106.98 s 
2024-03-31 10:35:37.787348:  
2024-03-31 10:35:37.787569: Epoch 830 
2024-03-31 10:35:37.787699: Current learning rate: 0.00203 
2024-03-31 10:37:22.533413: train_loss -0.6708 
2024-03-31 10:37:22.561951: val_loss -0.7125 
2024-03-31 10:37:22.562099: Pseudo dice [0.8698] 
2024-03-31 10:37:22.562344: Epoch time: 104.75 s 
2024-03-31 10:37:24.539127:  
2024-03-31 10:37:24.539291: Epoch 831 
2024-03-31 10:37:24.539407: Current learning rate: 0.00202 
2024-03-31 10:39:06.019751: train_loss -0.6845 
2024-03-31 10:39:06.086138: val_loss -0.6954 
2024-03-31 10:39:06.086293: Pseudo dice [0.869] 
2024-03-31 10:39:06.086387: Epoch time: 101.48 s 
2024-03-31 10:39:07.775555:  
2024-03-31 10:39:07.775830: Epoch 832 
2024-03-31 10:39:07.775920: Current learning rate: 0.00201 
2024-03-31 10:40:52.990806: train_loss -0.7008 
2024-03-31 10:40:53.110162: val_loss -0.6815 
2024-03-31 10:40:53.110330: Pseudo dice [0.8597] 
2024-03-31 10:40:53.110420: Epoch time: 105.22 s 
2024-03-31 10:40:55.065026:  
2024-03-31 10:40:55.065520: Epoch 833 
2024-03-31 10:40:55.065718: Current learning rate: 0.002 
2024-03-31 10:42:41.270050: train_loss -0.6817 
2024-03-31 10:42:41.320716: val_loss -0.6653 
2024-03-31 10:42:41.320855: Pseudo dice [0.8511] 
2024-03-31 10:42:41.320961: Epoch time: 106.21 s 
2024-03-31 10:42:44.005070:  
2024-03-31 10:42:44.005229: Epoch 834 
2024-03-31 10:42:44.005330: Current learning rate: 0.00199 
2024-03-31 10:44:32.552544: train_loss -0.6937 
2024-03-31 10:44:32.616726: val_loss -0.6981 
2024-03-31 10:44:32.616960: Pseudo dice [0.8537] 
2024-03-31 10:44:32.617065: Epoch time: 108.55 s 
2024-03-31 10:44:35.723462:  
2024-03-31 10:44:35.723993: Epoch 835 
2024-03-31 10:44:35.724117: Current learning rate: 0.00198 
2024-03-31 10:46:22.515051: train_loss -0.6844 
2024-03-31 10:46:22.565839: val_loss -0.6753 
2024-03-31 10:46:22.565988: Pseudo dice [0.8272] 
2024-03-31 10:46:22.566078: Epoch time: 106.79 s 
2024-03-31 10:46:24.557713:  
2024-03-31 10:46:24.557878: Epoch 836 
2024-03-31 10:46:24.558013: Current learning rate: 0.00196 
2024-03-31 10:48:10.903625: train_loss -0.6662 
2024-03-31 10:48:11.006590: val_loss -0.7092 
2024-03-31 10:48:11.006738: Pseudo dice [0.8662] 
2024-03-31 10:48:11.006820: Epoch time: 106.35 s 
2024-03-31 10:48:12.850649:  
2024-03-31 10:48:12.850818: Epoch 837 
2024-03-31 10:48:12.850951: Current learning rate: 0.00195 
2024-03-31 10:49:50.435942: train_loss -0.6753 
2024-03-31 10:49:50.489044: val_loss -0.7064 
2024-03-31 10:49:50.489212: Pseudo dice [0.8814] 
2024-03-31 10:49:50.489314: Epoch time: 97.59 s 
2024-03-31 10:49:52.302472:  
2024-03-31 10:49:52.302732: Epoch 838 
2024-03-31 10:49:52.302871: Current learning rate: 0.00194 
2024-03-31 10:51:33.348790: train_loss -0.6817 
2024-03-31 10:51:33.368050: val_loss -0.6728 
2024-03-31 10:51:33.368142: Pseudo dice [0.8673] 
2024-03-31 10:51:33.368209: Epoch time: 101.05 s 
2024-03-31 10:51:35.060221:  
2024-03-31 10:51:35.060427: Epoch 839 
2024-03-31 10:51:35.060533: Current learning rate: 0.00193 
2024-03-31 10:53:22.838987: train_loss -0.6834 
2024-03-31 10:53:22.870568: val_loss -0.6836 
2024-03-31 10:53:22.870687: Pseudo dice [0.8454] 
2024-03-31 10:53:22.870756: Epoch time: 107.78 s 
2024-03-31 10:53:24.748377:  
2024-03-31 10:53:24.748751: Epoch 840 
2024-03-31 10:53:24.749063: Current learning rate: 0.00192 
2024-03-31 10:55:11.023291: train_loss -0.6806 
2024-03-31 10:55:11.070117: val_loss -0.6953 
2024-03-31 10:55:11.070236: Pseudo dice [0.8534] 
2024-03-31 10:55:11.070323: Epoch time: 106.28 s 
2024-03-31 10:55:12.777042:  
2024-03-31 10:55:12.777184: Epoch 841 
2024-03-31 10:55:12.777288: Current learning rate: 0.00191 
2024-03-31 10:57:03.192597: train_loss -0.6671 
2024-03-31 10:57:03.228029: val_loss -0.6973 
2024-03-31 10:57:03.228302: Pseudo dice [0.8283] 
2024-03-31 10:57:03.228461: Epoch time: 110.42 s 
2024-03-31 10:57:06.087266:  
2024-03-31 10:57:06.087497: Epoch 842 
2024-03-31 10:57:06.087593: Current learning rate: 0.0019 
2024-03-31 10:58:58.977029: train_loss -0.6882 
2024-03-31 10:58:59.035794: val_loss -0.7098 
2024-03-31 10:58:59.035947: Pseudo dice [0.8533] 
2024-03-31 10:58:59.036034: Epoch time: 112.89 s 
2024-03-31 10:59:01.225396:  
2024-03-31 10:59:01.225682: Epoch 843 
2024-03-31 10:59:01.225771: Current learning rate: 0.00189 
2024-03-31 11:00:37.825910: train_loss -0.6717 
2024-03-31 11:00:37.910138: val_loss -0.7206 
2024-03-31 11:00:37.910419: Pseudo dice [0.8746] 
2024-03-31 11:00:37.910540: Epoch time: 96.6 s 
2024-03-31 11:00:39.803734:  
2024-03-31 11:00:39.803854: Epoch 844 
2024-03-31 11:00:39.803936: Current learning rate: 0.00188 
2024-03-31 11:02:20.380600: train_loss -0.6994 
2024-03-31 11:02:20.409724: val_loss -0.7186 
2024-03-31 11:02:20.409866: Pseudo dice [0.8699] 
2024-03-31 11:02:20.409947: Epoch time: 100.58 s 
2024-03-31 11:02:21.999417:  
2024-03-31 11:02:21.999538: Epoch 845 
2024-03-31 11:02:21.999614: Current learning rate: 0.00187 
2024-03-31 11:04:07.533023: train_loss -0.6888 
2024-03-31 11:04:07.601147: val_loss -0.6907 
2024-03-31 11:04:07.601562: Pseudo dice [0.8279] 
2024-03-31 11:04:07.601722: Epoch time: 105.53 s 
2024-03-31 11:04:09.588230:  
2024-03-31 11:04:09.588363: Epoch 846 
2024-03-31 11:04:09.588446: Current learning rate: 0.00186 
2024-03-31 11:05:54.352053: train_loss -0.6859 
2024-03-31 11:05:54.388946: val_loss -0.6958 
2024-03-31 11:05:54.389112: Pseudo dice [0.8594] 
2024-03-31 11:05:54.389269: Epoch time: 104.76 s 
2024-03-31 11:05:56.223080:  
2024-03-31 11:05:56.223453: Epoch 847 
2024-03-31 11:05:56.223541: Current learning rate: 0.00185 
2024-03-31 11:07:44.091602: train_loss -0.6733 
2024-03-31 11:07:44.141498: val_loss -0.6958 
2024-03-31 11:07:44.141618: Pseudo dice [0.8298] 
2024-03-31 11:07:44.141689: Epoch time: 107.87 s 
2024-03-31 11:07:47.814665:  
2024-03-31 11:07:47.814793: Epoch 848 
2024-03-31 11:07:47.814875: Current learning rate: 0.00184 
2024-03-31 11:09:36.011223: train_loss -0.6846 
2024-03-31 11:09:36.032570: val_loss -0.72 
2024-03-31 11:09:36.032867: Pseudo dice [0.8612] 
2024-03-31 11:09:36.032997: Epoch time: 108.2 s 
2024-03-31 11:09:38.088074:  
2024-03-31 11:09:38.088251: Epoch 849 
2024-03-31 11:09:38.088332: Current learning rate: 0.00182 
2024-03-31 11:11:21.692159: train_loss -0.6868 
2024-03-31 11:11:21.765399: val_loss -0.6817 
2024-03-31 11:11:21.765550: Pseudo dice [0.8562] 
2024-03-31 11:11:21.765640: Epoch time: 103.61 s 
2024-03-31 11:11:24.919245:  
2024-03-31 11:11:24.919388: Epoch 850 
2024-03-31 11:11:24.919476: Current learning rate: 0.00181 
2024-03-31 11:13:06.931952: train_loss -0.6841 
2024-03-31 11:13:07.011869: val_loss -0.7043 
2024-03-31 11:13:07.011993: Pseudo dice [0.8659] 
2024-03-31 11:13:07.012073: Epoch time: 102.01 s 
2024-03-31 11:13:09.234307:  
2024-03-31 11:13:09.234483: Epoch 851 
2024-03-31 11:13:09.234593: Current learning rate: 0.0018 
2024-03-31 11:14:57.286521: train_loss -0.6996 
2024-03-31 11:14:57.364398: val_loss -0.7195 
2024-03-31 11:14:57.364575: Pseudo dice [0.8559] 
2024-03-31 11:14:57.364688: Epoch time: 108.05 s 
2024-03-31 11:14:59.074535:  
2024-03-31 11:14:59.074660: Epoch 852 
2024-03-31 11:14:59.074737: Current learning rate: 0.00179 
2024-03-31 11:17:08.670573: train_loss -0.6792 
2024-03-31 11:17:08.747616: val_loss -0.6965 
2024-03-31 11:17:08.747770: Pseudo dice [0.8523] 
2024-03-31 11:17:08.747856: Epoch time: 129.6 s 
2024-03-31 11:17:13.570823:  
2024-03-31 11:17:13.572914: Epoch 853 
2024-03-31 11:17:13.575050: Current learning rate: 0.00178 
2024-03-31 11:21:21.009506: train_loss -0.6973 
2024-03-31 11:21:21.087112: val_loss -0.7235 
2024-03-31 11:21:21.087234: Pseudo dice [0.868] 
2024-03-31 11:21:21.087321: Epoch time: 247.51 s 
2024-03-31 11:21:22.998357:  
2024-03-31 11:21:22.998511: Epoch 854 
2024-03-31 11:21:22.998628: Current learning rate: 0.00177 
2024-03-31 11:24:13.561571: train_loss -0.701 
2024-03-31 11:24:13.627357: val_loss -0.6656 
2024-03-31 11:24:13.627512: Pseudo dice [0.8567] 
2024-03-31 11:24:13.627616: Epoch time: 170.52 s 
2024-03-31 11:24:17.110581:  
2024-03-31 11:24:17.110838: Epoch 855 
2024-03-31 11:24:17.110919: Current learning rate: 0.00176 
2024-03-31 11:26:06.888278: train_loss -0.6939 
2024-03-31 11:26:06.926313: val_loss -0.6931 
2024-03-31 11:26:06.926453: Pseudo dice [0.8296] 
2024-03-31 11:26:06.926547: Epoch time: 109.78 s 
2024-03-31 11:26:08.750026:  
2024-03-31 11:26:08.750178: Epoch 856 
2024-03-31 11:26:08.750262: Current learning rate: 0.00175 
2024-03-31 11:27:53.322156: train_loss -0.6872 
2024-03-31 11:27:53.322643: val_loss -0.6747 
2024-03-31 11:27:53.322760: Pseudo dice [0.8662] 
2024-03-31 11:27:53.322852: Epoch time: 104.57 s 
2024-03-31 11:27:55.538658:  
2024-03-31 11:27:55.538923: Epoch 857 
2024-03-31 11:27:55.539055: Current learning rate: 0.00174 
2024-03-31 11:29:35.233783: train_loss -0.688 
2024-03-31 11:29:35.347664: val_loss -0.7005 
2024-03-31 11:29:35.347805: Pseudo dice [0.8615] 
2024-03-31 11:29:35.347899: Epoch time: 99.7 s 
2024-03-31 11:29:37.807616:  
2024-03-31 11:29:37.807784: Epoch 858 
2024-03-31 11:29:37.807867: Current learning rate: 0.00173 
2024-03-31 11:31:16.368121: train_loss -0.6928 
2024-03-31 11:31:16.381216: val_loss -0.669 
2024-03-31 11:31:16.381352: Pseudo dice [0.8582] 
2024-03-31 11:31:16.381435: Epoch time: 98.56 s 
2024-03-31 11:31:18.723264:  
2024-03-31 11:31:18.723426: Epoch 859 
2024-03-31 11:31:18.723587: Current learning rate: 0.00172 
2024-03-31 11:32:57.518291: train_loss -0.6977 
2024-03-31 11:32:57.520027: val_loss -0.7139 
2024-03-31 11:32:57.520181: Pseudo dice [0.8648] 
2024-03-31 11:32:57.520360: Epoch time: 98.8 s 
2024-03-31 11:32:59.282742:  
2024-03-31 11:32:59.282961: Epoch 860 
2024-03-31 11:32:59.283050: Current learning rate: 0.0017 
2024-03-31 11:34:31.313681: train_loss -0.6783 
2024-03-31 11:34:31.347316: val_loss -0.702 
2024-03-31 11:34:31.347442: Pseudo dice [0.8612] 
2024-03-31 11:34:31.347571: Epoch time: 92.03 s 
2024-03-31 11:34:34.863605:  
2024-03-31 11:34:34.863808: Epoch 861 
2024-03-31 11:34:34.863941: Current learning rate: 0.00169 
2024-03-31 11:36:13.175065: train_loss -0.6965 
2024-03-31 11:36:13.220182: val_loss -0.6875 
2024-03-31 11:36:13.220376: Pseudo dice [0.8242] 
2024-03-31 11:36:13.220509: Epoch time: 98.31 s 
2024-03-31 11:36:15.219638:  
2024-03-31 11:36:15.219970: Epoch 862 
2024-03-31 11:36:15.220117: Current learning rate: 0.00168 
2024-03-31 11:37:56.460930: train_loss -0.673 
2024-03-31 11:37:56.461573: val_loss -0.7313 
2024-03-31 11:37:56.461671: Pseudo dice [0.8857] 
2024-03-31 11:37:56.461757: Epoch time: 101.24 s 
2024-03-31 11:37:58.909787:  
2024-03-31 11:37:58.909992: Epoch 863 
2024-03-31 11:37:58.910176: Current learning rate: 0.00167 
2024-03-31 11:39:36.030157: train_loss -0.7018 
2024-03-31 11:39:36.030685: val_loss -0.71 
2024-03-31 11:39:36.030773: Pseudo dice [0.861] 
2024-03-31 11:39:36.030851: Epoch time: 97.12 s 
2024-03-31 11:39:37.748585:  
2024-03-31 11:39:37.748835: Epoch 864 
2024-03-31 11:39:37.749032: Current learning rate: 0.00166 
2024-03-31 11:41:26.542365: train_loss -0.6909 
2024-03-31 11:41:26.635596: val_loss -0.6857 
2024-03-31 11:41:26.635769: Pseudo dice [0.8887] 
2024-03-31 11:41:26.635876: Epoch time: 108.79 s 
2024-03-31 11:41:28.562200:  
2024-03-31 11:41:28.562571: Epoch 865 
2024-03-31 11:41:28.562698: Current learning rate: 0.00165 
2024-03-31 11:43:18.771899: train_loss -0.6907 
2024-03-31 11:43:18.910254: val_loss -0.702 
2024-03-31 11:43:18.910457: Pseudo dice [0.8379] 
2024-03-31 11:43:18.910555: Epoch time: 110.21 s 
2024-03-31 11:43:21.701043:  
2024-03-31 11:43:21.701257: Epoch 866 
2024-03-31 11:43:21.701402: Current learning rate: 0.00164 
2024-03-31 11:44:58.094519: train_loss -0.7079 
2024-03-31 11:44:58.142920: val_loss -0.7163 
2024-03-31 11:44:58.143078: Pseudo dice [0.8438] 
2024-03-31 11:44:58.143173: Epoch time: 96.39 s 
2024-03-31 11:44:59.483415:  
2024-03-31 11:44:59.483589: Epoch 867 
2024-03-31 11:44:59.483677: Current learning rate: 0.00163 
2024-03-31 11:46:33.154004: train_loss -0.6971 
2024-03-31 11:46:33.155120: val_loss -0.7058 
2024-03-31 11:46:33.155212: Pseudo dice [0.8591] 
2024-03-31 11:46:33.155290: Epoch time: 93.67 s 
2024-03-31 11:46:34.881147:  
2024-03-31 11:46:34.881578: Epoch 868 
2024-03-31 11:46:34.881690: Current learning rate: 0.00162 
2024-03-31 11:48:18.557866: train_loss -0.7018 
2024-03-31 11:48:18.652092: val_loss -0.6899 
2024-03-31 11:48:18.652420: Pseudo dice [0.8696] 
2024-03-31 11:48:18.652601: Epoch time: 103.68 s 
2024-03-31 11:48:21.091513:  
2024-03-31 11:48:21.091701: Epoch 869 
2024-03-31 11:48:21.091810: Current learning rate: 0.00161 
2024-03-31 11:50:03.651608: train_loss -0.6968 
2024-03-31 11:50:03.667732: val_loss -0.7139 
2024-03-31 11:50:03.667857: Pseudo dice [0.8575] 
2024-03-31 11:50:03.667990: Epoch time: 102.56 s 
2024-03-31 11:50:05.704319:  
2024-03-31 11:50:05.704590: Epoch 870 
2024-03-31 11:50:05.704683: Current learning rate: 0.00159 
2024-03-31 11:51:50.666732: train_loss -0.6852 
2024-03-31 11:51:50.667214: val_loss -0.7269 
2024-03-31 11:51:50.667318: Pseudo dice [0.8734] 
2024-03-31 11:51:50.711642: Epoch time: 104.96 s 
2024-03-31 11:51:52.945435:  
2024-03-31 11:51:52.945709: Epoch 871 
2024-03-31 11:51:52.945921: Current learning rate: 0.00158 
2024-03-31 11:53:40.864040: train_loss -0.7053 
2024-03-31 11:53:40.864577: val_loss -0.6846 
2024-03-31 11:53:40.864654: Pseudo dice [0.8645] 
2024-03-31 11:53:40.864739: Epoch time: 107.92 s 
2024-03-31 11:53:42.727537:  
2024-03-31 11:53:42.727685: Epoch 872 
2024-03-31 11:53:42.727849: Current learning rate: 0.00157 
2024-03-31 11:55:26.030712: train_loss -0.6946 
2024-03-31 11:55:26.056323: val_loss -0.7328 
2024-03-31 11:55:26.056437: Pseudo dice [0.8705] 
2024-03-31 11:55:26.056530: Epoch time: 103.3 s 
2024-03-31 11:55:27.707444:  
2024-03-31 11:55:27.707593: Epoch 873 
2024-03-31 11:55:27.707694: Current learning rate: 0.00156 
2024-03-31 11:57:01.366653: train_loss -0.7075 
2024-03-31 11:57:01.440713: val_loss -0.7224 
2024-03-31 11:57:01.440842: Pseudo dice [0.8567] 
2024-03-31 11:57:01.440931: Epoch time: 93.66 s 
2024-03-31 11:57:03.299281:  
2024-03-31 11:57:03.299497: Epoch 874 
2024-03-31 11:57:03.299690: Current learning rate: 0.00155 
2024-03-31 11:58:49.341918: train_loss -0.6932 
2024-03-31 11:58:49.357241: val_loss -0.7405 
2024-03-31 11:58:49.357380: Pseudo dice [0.8631] 
2024-03-31 11:58:49.357470: Epoch time: 106.04 s 
2024-03-31 11:58:52.300581:  
2024-03-31 11:58:52.300859: Epoch 875 
2024-03-31 11:58:52.300986: Current learning rate: 0.00154 
2024-03-31 12:00:40.435957: train_loss -0.7184 
2024-03-31 12:00:40.444403: val_loss -0.7148 
2024-03-31 12:00:40.444493: Pseudo dice [0.8811] 
2024-03-31 12:00:40.444568: Epoch time: 108.14 s 
2024-03-31 12:00:40.444623: Yayy! New best EMA pseudo Dice: 0.863 
2024-03-31 12:00:44.360469:  
2024-03-31 12:00:44.360960: Epoch 876 
2024-03-31 12:00:44.361091: Current learning rate: 0.00153 
2024-03-31 12:02:23.452270: train_loss -0.6965 
2024-03-31 12:02:23.464733: val_loss -0.7227 
2024-03-31 12:02:23.464885: Pseudo dice [0.871] 
2024-03-31 12:02:23.487955: Epoch time: 99.09 s 
2024-03-31 12:02:23.488203: Yayy! New best EMA pseudo Dice: 0.8638 
2024-03-31 12:02:26.980210:  
2024-03-31 12:02:26.980366: Epoch 877 
2024-03-31 12:02:26.980455: Current learning rate: 0.00152 
2024-03-31 12:04:03.119649: train_loss -0.7016 
2024-03-31 12:04:03.147337: val_loss -0.7331 
2024-03-31 12:04:03.147472: Pseudo dice [0.8562] 
2024-03-31 12:04:03.147558: Epoch time: 96.14 s 
2024-03-31 12:04:05.688572:  
2024-03-31 12:04:05.688797: Epoch 878 
2024-03-31 12:04:05.688957: Current learning rate: 0.00151 
2024-03-31 12:05:55.076165: train_loss -0.6806 
2024-03-31 12:05:55.089106: val_loss -0.7083 
2024-03-31 12:05:55.089242: Pseudo dice [0.8502] 
2024-03-31 12:05:55.089329: Epoch time: 109.39 s 
2024-03-31 12:05:57.531821:  
2024-03-31 12:05:57.531977: Epoch 879 
2024-03-31 12:05:57.532077: Current learning rate: 0.00149 
2024-03-31 12:07:30.042208: train_loss -0.6969 
2024-03-31 12:07:30.166612: val_loss -0.7229 
2024-03-31 12:07:30.166724: Pseudo dice [0.8306] 
2024-03-31 12:07:30.166814: Epoch time: 92.51 s 
2024-03-31 12:07:32.174016:  
2024-03-31 12:07:32.174337: Epoch 880 
2024-03-31 12:07:32.174534: Current learning rate: 0.00148 
2024-03-31 12:09:15.778497: train_loss -0.6906 
2024-03-31 12:09:15.794851: val_loss -0.7139 
2024-03-31 12:09:15.794979: Pseudo dice [0.8642] 
2024-03-31 12:09:15.818830: Epoch time: 103.61 s 
2024-03-31 12:09:17.405501:  
2024-03-31 12:09:17.405761: Epoch 881 
2024-03-31 12:09:17.405899: Current learning rate: 0.00147 
2024-03-31 12:11:04.387508: train_loss -0.6696 
2024-03-31 12:11:04.410212: val_loss -0.7121 
2024-03-31 12:11:04.410362: Pseudo dice [0.8574] 
2024-03-31 12:11:04.410467: Epoch time: 106.98 s 
2024-03-31 12:11:07.535216:  
2024-03-31 12:11:07.535347: Epoch 882 
2024-03-31 12:11:07.535430: Current learning rate: 0.00146 
2024-03-31 12:12:51.904050: train_loss -0.6921 
2024-03-31 12:12:51.931454: val_loss -0.7265 
2024-03-31 12:12:51.931580: Pseudo dice [0.878] 
2024-03-31 12:12:51.931654: Epoch time: 104.37 s 
2024-03-31 12:12:53.736058:  
2024-03-31 12:12:53.736473: Epoch 883 
2024-03-31 12:12:53.736619: Current learning rate: 0.00145 
2024-03-31 12:14:36.484557: train_loss -0.6918 
2024-03-31 12:14:36.485088: val_loss -0.7328 
2024-03-31 12:14:36.485211: Pseudo dice [0.8918] 
2024-03-31 12:14:36.485299: Epoch time: 102.75 s 
2024-03-31 12:14:36.485358: Yayy! New best EMA pseudo Dice: 0.864 
2024-03-31 12:14:40.035262:  
2024-03-31 12:14:40.035388: Epoch 884 
2024-03-31 12:14:40.035468: Current learning rate: 0.00144 
2024-03-31 12:16:22.274731: train_loss -0.7026 
2024-03-31 12:16:22.284253: val_loss -0.7276 
2024-03-31 12:16:22.284416: Pseudo dice [0.8545] 
2024-03-31 12:16:22.315064: Epoch time: 102.24 s 
2024-03-31 12:16:23.805482:  
2024-03-31 12:16:23.805605: Epoch 885 
2024-03-31 12:16:23.805682: Current learning rate: 0.00143 
2024-03-31 12:17:57.800726: train_loss -0.6911 
2024-03-31 12:17:57.815882: val_loss -0.7244 
2024-03-31 12:17:57.816003: Pseudo dice [0.8271] 
2024-03-31 12:17:57.816076: Epoch time: 94.0 s 
2024-03-31 12:17:59.766031:  
2024-03-31 12:17:59.766277: Epoch 886 
2024-03-31 12:17:59.766472: Current learning rate: 0.00142 
2024-03-31 12:19:42.050630: train_loss -0.701 
2024-03-31 12:19:42.231007: val_loss -0.7259 
2024-03-31 12:19:42.231184: Pseudo dice [0.8886] 
2024-03-31 12:19:42.257094: Epoch time: 102.29 s 
2024-03-31 12:19:44.473380:  
2024-03-31 12:19:44.473515: Epoch 887 
2024-03-31 12:19:44.473613: Current learning rate: 0.00141 
2024-03-31 12:21:31.532896: train_loss -0.6931 
2024-03-31 12:21:31.549576: val_loss -0.7229 
2024-03-31 12:21:31.549912: Pseudo dice [0.8673] 
2024-03-31 12:21:31.550246: Epoch time: 107.06 s 
2024-03-31 12:21:34.395279:  
2024-03-31 12:21:34.395572: Epoch 888 
2024-03-31 12:21:34.395658: Current learning rate: 0.00139 
2024-03-31 12:23:20.089122: train_loss -0.7158 
2024-03-31 12:23:20.124907: val_loss -0.7158 
2024-03-31 12:23:20.125192: Pseudo dice [0.8654] 
2024-03-31 12:23:20.125317: Epoch time: 105.69 s 
2024-03-31 12:23:21.729159:  
2024-03-31 12:23:21.729343: Epoch 889 
2024-03-31 12:23:21.729473: Current learning rate: 0.00138 
2024-03-31 12:25:02.466004: train_loss -0.6908 
2024-03-31 12:25:02.497373: val_loss -0.7197 
2024-03-31 12:25:02.497657: Pseudo dice [0.8519] 
2024-03-31 12:25:02.497861: Epoch time: 100.74 s 
2024-03-31 12:25:04.388181:  
2024-03-31 12:25:04.388339: Epoch 890 
2024-03-31 12:25:04.388424: Current learning rate: 0.00137 
2024-03-31 12:26:48.567281: train_loss -0.694 
2024-03-31 12:26:48.591298: val_loss -0.6951 
2024-03-31 12:26:48.591365: Pseudo dice [0.83] 
2024-03-31 12:26:48.604446: Epoch time: 104.18 s 
2024-03-31 12:26:50.343383:  
2024-03-31 12:26:50.343789: Epoch 891 
2024-03-31 12:26:50.343915: Current learning rate: 0.00136 
2024-03-31 12:28:32.191632: train_loss -0.684 
2024-03-31 12:28:32.208241: val_loss -0.7162 
2024-03-31 12:28:32.208384: Pseudo dice [0.8638] 
2024-03-31 12:28:32.208495: Epoch time: 101.85 s 
2024-03-31 12:28:33.831742:  
2024-03-31 12:28:33.831916: Epoch 892 
2024-03-31 12:28:33.832118: Current learning rate: 0.00135 
2024-03-31 12:30:16.688879: train_loss -0.7048 
2024-03-31 12:30:16.714659: val_loss -0.7199 
2024-03-31 12:30:16.714841: Pseudo dice [0.8788] 
2024-03-31 12:30:16.714974: Epoch time: 102.86 s 
2024-03-31 12:30:19.157300:  
2024-03-31 12:30:19.157547: Epoch 893 
2024-03-31 12:30:19.157739: Current learning rate: 0.00134 
2024-03-31 12:38:10.748586: train_loss -0.7045 
2024-03-31 12:38:10.764866: val_loss -0.7323 
2024-03-31 12:38:10.766655: Pseudo dice [0.8718] 
2024-03-31 12:38:10.767622: Epoch time: 471.59 s 
2024-03-31 12:38:35.852389:  
2024-03-31 12:38:35.852629: Epoch 894 
2024-03-31 12:38:35.852823: Current learning rate: 0.00133 
2024-03-31 12:49:11.473878: train_loss -0.6892 
2024-03-31 12:49:11.474419: val_loss -0.7145 
2024-03-31 12:49:11.474612: Pseudo dice [0.8706] 
2024-03-31 12:49:11.474773: Epoch time: 635.63 s 
2024-03-31 12:49:29.245329:  
2024-03-31 12:49:29.249720: Epoch 895 
2024-03-31 12:49:29.252251: Current learning rate: 0.00132 
2024-03-31 12:59:00.484656: train_loss -0.6951 
2024-03-31 12:59:00.485444: val_loss -0.7339 
2024-03-31 12:59:00.486018: Pseudo dice [0.8835] 
2024-03-31 12:59:00.486380: Epoch time: 571.27 s 
2024-03-31 12:59:00.486777: Yayy! New best EMA pseudo Dice: 0.8652 
2024-03-31 12:59:15.586873:  
2024-03-31 12:59:15.588049: Epoch 896 
2024-03-31 12:59:15.588856: Current learning rate: 0.0013 
2024-03-31 13:11:37.985791: train_loss -0.6981 
2024-03-31 13:11:38.000111: val_loss -0.7029 
2024-03-31 13:11:38.038626: Pseudo dice [0.8811] 
2024-03-31 13:11:38.054497: Epoch time: 742.41 s 
2024-03-31 13:11:38.059670: Yayy! New best EMA pseudo Dice: 0.8668 
2024-03-31 13:12:06.215710:  
2024-03-31 13:12:06.216260: Epoch 897 
2024-03-31 13:12:06.216625: Current learning rate: 0.00129 
2024-03-31 13:22:08.834059: train_loss -0.6883 
2024-03-31 13:22:08.851015: val_loss -0.7101 
2024-03-31 13:22:08.851470: Pseudo dice [0.8634] 
2024-03-31 13:22:08.852096: Epoch time: 602.64 s 
2024-03-31 13:22:30.004858:  
2024-03-31 13:22:30.005290: Epoch 898 
2024-03-31 13:22:30.005478: Current learning rate: 0.00128 
2024-03-31 13:33:12.558963: train_loss -0.6997 
2024-03-31 13:33:12.559519: val_loss -0.7108 
2024-03-31 13:33:12.559595: Pseudo dice [0.8546] 
2024-03-31 13:33:12.559718: Epoch time: 642.57 s 
2024-03-31 13:33:15.210327:  
2024-03-31 13:33:15.210500: Epoch 899 
2024-03-31 13:33:15.210609: Current learning rate: 0.00127 
2024-03-31 13:44:54.954148: train_loss -0.7042 
2024-03-31 13:44:55.231869: val_loss -0.7423 
2024-03-31 13:44:55.257316: Pseudo dice [0.866] 
2024-03-31 13:44:55.261154: Epoch time: 699.74 s 
2024-03-31 13:45:18.494276:  
2024-03-31 13:45:18.494519: Epoch 900 
2024-03-31 13:45:18.494648: Current learning rate: 0.00126 
2024-03-31 13:51:46.750566: train_loss -0.7002 
2024-03-31 13:51:46.757993: val_loss -0.7064 
2024-03-31 13:51:46.758140: Pseudo dice [0.8726] 
2024-03-31 13:51:46.769179: Epoch time: 388.26 s 
2024-03-31 13:51:48.861621:  
2024-03-31 13:51:48.861794: Epoch 901 
2024-03-31 13:51:48.861912: Current learning rate: 0.00125 
2024-03-31 13:53:30.011992: train_loss -0.725 
2024-03-31 13:53:30.036464: val_loss -0.7351 
2024-03-31 13:53:30.036615: Pseudo dice [0.8809] 
2024-03-31 13:53:30.036709: Epoch time: 101.15 s 
2024-03-31 13:53:30.036835: Yayy! New best EMA pseudo Dice: 0.8675 
2024-03-31 13:53:34.011851:  
2024-03-31 13:53:34.012096: Epoch 902 
2024-03-31 13:53:34.012187: Current learning rate: 0.00124 
2024-03-31 13:55:10.926103: train_loss -0.7044 
2024-03-31 13:55:10.965025: val_loss -0.768 
2024-03-31 13:55:10.965277: Pseudo dice [0.8788] 
2024-03-31 13:55:10.965370: Epoch time: 96.91 s 
2024-03-31 13:55:10.965439: Yayy! New best EMA pseudo Dice: 0.8686 
2024-03-31 13:55:13.270914:  
2024-03-31 13:55:13.271075: Epoch 903 
2024-03-31 13:55:13.271151: Current learning rate: 0.00122 
2024-03-31 13:56:47.946567: train_loss -0.7053 
2024-03-31 13:56:47.961408: val_loss -0.7216 
2024-03-31 13:56:47.961549: Pseudo dice [0.879] 
2024-03-31 13:56:47.961635: Epoch time: 94.68 s 
2024-03-31 13:56:47.961686: Yayy! New best EMA pseudo Dice: 0.8697 
2024-03-31 13:56:50.776586:  
2024-03-31 13:56:50.776741: Epoch 904 
2024-03-31 13:56:50.776824: Current learning rate: 0.00121 
2024-03-31 13:58:31.920596: train_loss -0.7037 
2024-03-31 13:58:31.941338: val_loss -0.732 
2024-03-31 13:58:31.941501: Pseudo dice [0.8541] 
2024-03-31 13:58:31.941689: Epoch time: 101.14 s 
2024-03-31 13:58:34.035268:  
2024-03-31 13:58:34.035455: Epoch 905 
2024-03-31 13:58:34.035583: Current learning rate: 0.0012 
2024-03-31 14:00:02.702074: train_loss -0.7163 
2024-03-31 14:00:02.720144: val_loss -0.7348 
2024-03-31 14:00:02.720309: Pseudo dice [0.8726] 
2024-03-31 14:00:02.720401: Epoch time: 88.67 s 
2024-03-31 14:00:04.479320:  
2024-03-31 14:00:04.479453: Epoch 906 
2024-03-31 14:00:04.479554: Current learning rate: 0.00119 
2024-03-31 14:01:38.909945: train_loss -0.7118 
2024-03-31 14:01:38.910253: val_loss -0.7496 
2024-03-31 14:01:38.910341: Pseudo dice [0.8699] 
2024-03-31 14:01:38.910450: Epoch time: 94.43 s 
2024-03-31 14:01:40.682168:  
2024-03-31 14:01:40.682392: Epoch 907 
2024-03-31 14:01:40.682538: Current learning rate: 0.00118 
2024-03-31 14:03:29.017059: train_loss -0.7168 
2024-03-31 14:03:29.046983: val_loss -0.7144 
2024-03-31 14:03:29.047176: Pseudo dice [0.8501] 
2024-03-31 14:03:29.047307: Epoch time: 108.34 s 
2024-03-31 14:03:31.344636:  
2024-03-31 14:03:31.345268: Epoch 908 
2024-03-31 14:03:31.345374: Current learning rate: 0.00117 
2024-03-31 14:05:15.295057: train_loss -0.6982 
2024-03-31 14:05:15.310698: val_loss -0.7008 
2024-03-31 14:05:15.310916: Pseudo dice [0.8518] 
2024-03-31 14:05:15.311065: Epoch time: 103.95 s 
2024-03-31 14:05:17.784161:  
2024-03-31 14:05:17.784768: Epoch 909 
2024-03-31 14:05:17.784896: Current learning rate: 0.00116 
2024-03-31 14:06:57.901183: train_loss -0.7035 
2024-03-31 14:06:57.911392: val_loss -0.7335 
2024-03-31 14:06:57.911532: Pseudo dice [0.8761] 
2024-03-31 14:06:57.911707: Epoch time: 100.12 s 
2024-03-31 14:06:59.929849:  
2024-03-31 14:06:59.930048: Epoch 910 
2024-03-31 14:06:59.930246: Current learning rate: 0.00115 
2024-03-31 14:08:42.764908: train_loss -0.693 
2024-03-31 14:08:42.854955: val_loss -0.7164 
2024-03-31 14:08:42.855115: Pseudo dice [0.8605] 
2024-03-31 14:08:42.870241: Epoch time: 102.84 s 
2024-03-31 14:08:44.564195:  
2024-03-31 14:08:44.564338: Epoch 911 
2024-03-31 14:08:44.564450: Current learning rate: 0.00113 
2024-03-31 14:10:21.598549: train_loss -0.6888 
2024-03-31 14:10:21.635760: val_loss -0.7363 
2024-03-31 14:10:21.635953: Pseudo dice [0.8371] 
2024-03-31 14:10:21.636083: Epoch time: 97.04 s 
2024-03-31 14:10:23.819925:  
2024-03-31 14:10:23.820243: Epoch 912 
2024-03-31 14:10:23.820330: Current learning rate: 0.00112 
2024-03-31 14:12:03.295687: train_loss -0.7059 
2024-03-31 14:12:03.348857: val_loss -0.7234 
2024-03-31 14:12:03.349122: Pseudo dice [0.8834] 
2024-03-31 14:12:03.349256: Epoch time: 99.48 s 
2024-03-31 14:12:05.366397:  
2024-03-31 14:12:05.366615: Epoch 913 
2024-03-31 14:12:05.366712: Current learning rate: 0.00111 
2024-03-31 14:13:45.076291: train_loss -0.7032 
2024-03-31 14:13:45.151702: val_loss -0.7484 
2024-03-31 14:13:45.152122: Pseudo dice [0.8753] 
2024-03-31 14:13:45.152295: Epoch time: 99.71 s 
2024-03-31 14:13:47.239054:  
2024-03-31 14:13:47.239290: Epoch 914 
2024-03-31 14:13:47.239388: Current learning rate: 0.0011 
2024-03-31 14:15:31.734956: train_loss -0.6987 
2024-03-31 14:15:31.786744: val_loss -0.7343 
2024-03-31 14:15:31.786907: Pseudo dice [0.8794] 
2024-03-31 14:15:31.787013: Epoch time: 104.5 s 
2024-03-31 14:15:34.219967:  
2024-03-31 14:15:34.220275: Epoch 915 
2024-03-31 14:15:34.220380: Current learning rate: 0.00109 
2024-03-31 14:17:11.827578: train_loss -0.7184 
2024-03-31 14:17:11.842882: val_loss -0.7204 
2024-03-31 14:17:11.843079: Pseudo dice [0.8454] 
2024-03-31 14:17:11.843176: Epoch time: 97.61 s 
2024-03-31 14:17:13.910443:  
2024-03-31 14:17:13.910784: Epoch 916 
2024-03-31 14:17:13.910975: Current learning rate: 0.00108 
2024-03-31 14:18:55.615491: train_loss -0.7011 
2024-03-31 14:18:55.649129: val_loss -0.702 
2024-03-31 14:18:55.649338: Pseudo dice [0.8251] 
2024-03-31 14:18:55.649769: Epoch time: 101.71 s 
2024-03-31 14:18:57.842243:  
2024-03-31 14:18:57.842443: Epoch 917 
2024-03-31 14:18:57.842532: Current learning rate: 0.00106 
2024-03-31 14:20:39.382311: train_loss -0.7064 
2024-03-31 14:20:39.404946: val_loss -0.7364 
2024-03-31 14:20:39.405270: Pseudo dice [0.8956] 
2024-03-31 14:20:39.405415: Epoch time: 101.54 s 
2024-03-31 14:20:41.902907:  
2024-03-31 14:20:41.903100: Epoch 918 
2024-03-31 14:20:41.903189: Current learning rate: 0.00105 
2024-03-31 14:22:13.749204: train_loss -0.7177 
2024-03-31 14:22:13.749549: val_loss -0.7453 
2024-03-31 14:22:13.749645: Pseudo dice [0.8751] 
2024-03-31 14:22:13.749736: Epoch time: 91.85 s 
2024-03-31 14:22:15.525467:  
2024-03-31 14:22:15.525686: Epoch 919 
2024-03-31 14:22:15.525775: Current learning rate: 0.00104 
2024-03-31 14:23:53.315080: train_loss -0.7274 
2024-03-31 14:23:53.334066: val_loss -0.7375 
2024-03-31 14:23:53.334241: Pseudo dice [0.8713] 
2024-03-31 14:23:53.334339: Epoch time: 97.79 s 
2024-03-31 14:23:55.387605:  
2024-03-31 14:23:55.387822: Epoch 920 
2024-03-31 14:23:55.387908: Current learning rate: 0.00103 
2024-03-31 14:25:35.268162: train_loss -0.7177 
2024-03-31 14:25:35.268551: val_loss -0.7451 
2024-03-31 14:25:35.268665: Pseudo dice [0.8839] 
2024-03-31 14:25:35.268780: Epoch time: 99.88 s 
2024-03-31 14:25:37.846115:  
2024-03-31 14:25:37.846309: Epoch 921 
2024-03-31 14:25:37.846444: Current learning rate: 0.00102 
2024-03-31 14:27:14.616664: train_loss -0.7234 
2024-03-31 14:27:14.646452: val_loss -0.7469 
2024-03-31 14:27:14.646603: Pseudo dice [0.8562] 
2024-03-31 14:27:14.646688: Epoch time: 96.77 s 
2024-03-31 14:27:16.813556:  
2024-03-31 14:27:16.813707: Epoch 922 
2024-03-31 14:27:16.813845: Current learning rate: 0.00101 
2024-03-31 14:28:51.610015: train_loss -0.7101 
2024-03-31 14:28:51.794467: val_loss -0.7223 
2024-03-31 14:28:51.794677: Pseudo dice [0.8747] 
2024-03-31 14:28:51.794776: Epoch time: 94.8 s 
2024-03-31 14:28:54.721421:  
2024-03-31 14:28:54.721557: Epoch 923 
2024-03-31 14:28:54.721645: Current learning rate: 0.001 
2024-03-31 14:30:34.307731: train_loss -0.7109 
2024-03-31 14:30:34.308038: val_loss -0.7483 
2024-03-31 14:30:34.308119: Pseudo dice [0.8839] 
2024-03-31 14:30:34.308233: Epoch time: 99.59 s 
2024-03-31 14:30:36.172838:  
2024-03-31 14:30:36.173062: Epoch 924 
2024-03-31 14:30:36.173228: Current learning rate: 0.00098 
2024-03-31 14:32:22.931495: train_loss -0.6958 
2024-03-31 14:32:22.973305: val_loss -0.7132 
2024-03-31 14:32:22.973625: Pseudo dice [0.8659] 
2024-03-31 14:32:22.973818: Epoch time: 106.76 s 
2024-03-31 14:32:25.776723:  
2024-03-31 14:32:25.777351: Epoch 925 
2024-03-31 14:32:25.777562: Current learning rate: 0.00097 
2024-03-31 14:33:56.746117: train_loss -0.7037 
2024-03-31 14:33:56.746588: val_loss -0.7373 
2024-03-31 14:33:56.746731: Pseudo dice [0.8795] 
2024-03-31 14:33:56.746930: Epoch time: 90.97 s 
2024-03-31 14:33:56.747007: Yayy! New best EMA pseudo Dice: 0.87 
2024-03-31 14:34:00.209599:  
2024-03-31 14:34:00.209798: Epoch 926 
2024-03-31 14:34:00.209893: Current learning rate: 0.00096 
2024-03-31 14:35:32.247567: train_loss -0.7099 
2024-03-31 14:35:32.386021: val_loss -0.7235 
2024-03-31 14:35:32.386249: Pseudo dice [0.8523] 
2024-03-31 14:35:32.386426: Epoch time: 92.04 s 
2024-03-31 14:35:34.137232:  
2024-03-31 14:35:34.137360: Epoch 927 
2024-03-31 14:35:34.137447: Current learning rate: 0.00095 
2024-03-31 14:37:09.882039: train_loss -0.7275 
2024-03-31 14:37:09.930256: val_loss -0.7519 
2024-03-31 14:37:09.930431: Pseudo dice [0.8688] 
2024-03-31 14:37:09.930667: Epoch time: 95.75 s 
2024-03-31 14:37:11.848396:  
2024-03-31 14:37:11.848597: Epoch 928 
2024-03-31 14:37:11.848731: Current learning rate: 0.00094 
2024-03-31 14:38:50.191646: train_loss -0.7154 
2024-03-31 14:38:50.253446: val_loss -0.7309 
2024-03-31 14:38:50.253618: Pseudo dice [0.8749] 
2024-03-31 14:38:50.253710: Epoch time: 98.34 s 
2024-03-31 14:38:53.503435:  
2024-03-31 14:38:53.503712: Epoch 929 
2024-03-31 14:38:53.503833: Current learning rate: 0.00092 
2024-03-31 14:40:29.155017: train_loss -0.7012 
2024-03-31 14:40:29.155338: val_loss -0.7256 
2024-03-31 14:40:29.155418: Pseudo dice [0.8744] 
2024-03-31 14:40:29.155505: Epoch time: 95.66 s 
2024-03-31 14:40:30.989381:  
2024-03-31 14:40:30.989553: Epoch 930 
2024-03-31 14:40:30.989688: Current learning rate: 0.00091 
2024-03-31 14:42:05.773348: train_loss -0.7328 
2024-03-31 14:42:05.783461: val_loss -0.7566 
2024-03-31 14:42:05.783609: Pseudo dice [0.8885] 
2024-03-31 14:42:05.783699: Epoch time: 94.78 s 
2024-03-31 14:42:05.783753: Yayy! New best EMA pseudo Dice: 0.8714 
2024-03-31 14:42:09.016063:  
2024-03-31 14:42:09.016302: Epoch 931 
2024-03-31 14:42:09.016443: Current learning rate: 0.0009 
2024-03-31 14:43:51.277557: train_loss -0.7174 
2024-03-31 14:43:51.289983: val_loss -0.7319 
2024-03-31 14:43:51.290173: Pseudo dice [0.8777] 
2024-03-31 14:43:51.290329: Epoch time: 102.26 s 
2024-03-31 14:43:51.290444: Yayy! New best EMA pseudo Dice: 0.872 
2024-03-31 14:43:53.938625:  
2024-03-31 14:43:53.938829: Epoch 932 
2024-03-31 14:43:53.938914: Current learning rate: 0.00089 
2024-03-31 14:45:24.359017: train_loss -0.7156 
2024-03-31 14:45:24.371741: val_loss -0.7401 
2024-03-31 14:45:24.371900: Pseudo dice [0.8806] 
2024-03-31 14:45:24.371990: Epoch time: 90.42 s 
2024-03-31 14:45:24.372047: Yayy! New best EMA pseudo Dice: 0.8729 
2024-03-31 14:45:27.145026:  
2024-03-31 14:45:27.146267: Epoch 933 
2024-03-31 14:45:27.146386: Current learning rate: 0.00088 
2024-03-31 14:46:57.658340: train_loss -0.7128 
2024-03-31 14:46:57.678348: val_loss -0.7144 
2024-03-31 14:46:57.678498: Pseudo dice [0.8545] 
2024-03-31 14:46:57.678587: Epoch time: 90.51 s 
2024-03-31 14:46:59.373443:  
2024-03-31 14:46:59.373676: Epoch 934 
2024-03-31 14:46:59.373837: Current learning rate: 0.00087 
2024-03-31 14:48:30.757151: train_loss -0.7183 
2024-03-31 14:48:30.801740: val_loss -0.7278 
2024-03-31 14:48:30.801880: Pseudo dice [0.8676] 
2024-03-31 14:48:30.801963: Epoch time: 91.39 s 
2024-03-31 14:48:33.045843:  
2024-03-31 14:48:33.046047: Epoch 935 
2024-03-31 14:48:33.046167: Current learning rate: 0.00085 
2024-03-31 14:50:12.197523: train_loss -0.707 
2024-03-31 14:50:12.233393: val_loss -0.7582 
2024-03-31 14:50:12.233536: Pseudo dice [0.8806] 
2024-03-31 14:50:12.233636: Epoch time: 99.15 s 
2024-03-31 14:50:14.499650:  
2024-03-31 14:50:14.499904: Epoch 936 
2024-03-31 14:50:14.499992: Current learning rate: 0.00084 
2024-03-31 14:51:54.668562: train_loss -0.7056 
2024-03-31 14:51:54.669515: val_loss -0.7208 
2024-03-31 14:51:54.669627: Pseudo dice [0.8751] 
2024-03-31 14:51:54.669760: Epoch time: 100.17 s 
2024-03-31 14:51:56.586154:  
2024-03-31 14:51:56.586403: Epoch 937 
2024-03-31 14:51:56.586516: Current learning rate: 0.00083 
2024-03-31 14:53:32.759853: train_loss -0.7131 
2024-03-31 14:53:32.830057: val_loss -0.6981 
2024-03-31 14:53:32.830271: Pseudo dice [0.8758] 
2024-03-31 14:53:32.830410: Epoch time: 96.17 s 
2024-03-31 14:53:35.021010:  
2024-03-31 14:53:35.021360: Epoch 938 
2024-03-31 14:53:35.021625: Current learning rate: 0.00082 
2024-03-31 14:55:11.544785: train_loss -0.7051 
2024-03-31 14:55:11.579585: val_loss -0.7263 
2024-03-31 14:55:11.579705: Pseudo dice [0.8709] 
2024-03-31 14:55:11.579772: Epoch time: 96.53 s 
2024-03-31 14:55:13.241457:  
2024-03-31 14:55:13.241602: Epoch 939 
2024-03-31 14:55:13.241776: Current learning rate: 0.00081 
2024-03-31 14:56:42.595836: train_loss -0.7138 
2024-03-31 14:56:42.596115: val_loss -0.7349 
2024-03-31 14:56:42.613229: Pseudo dice [0.8774] 
2024-03-31 14:56:42.625294: Epoch time: 89.36 s 
2024-03-31 14:56:44.592359:  
2024-03-31 14:56:44.592517: Epoch 940 
2024-03-31 14:56:44.592648: Current learning rate: 0.00079 
2024-03-31 14:58:25.935362: train_loss -0.7224 
2024-03-31 14:58:25.950969: val_loss -0.7285 
2024-03-31 14:58:25.951214: Pseudo dice [0.8711] 
2024-03-31 14:58:25.951367: Epoch time: 101.34 s 
2024-03-31 14:58:28.611156:  
2024-03-31 14:58:28.611552: Epoch 941 
2024-03-31 14:58:28.611704: Current learning rate: 0.00078 
2024-03-31 15:00:05.663682: train_loss -0.7256 
2024-03-31 15:00:05.680964: val_loss -0.7181 
2024-03-31 15:00:05.681139: Pseudo dice [0.8646] 
2024-03-31 15:00:05.681235: Epoch time: 97.05 s 
2024-03-31 15:00:07.827043:  
2024-03-31 15:00:07.827332: Epoch 942 
2024-03-31 15:00:07.827507: Current learning rate: 0.00077 
2024-03-31 15:01:51.077225: train_loss -0.7186 
2024-03-31 15:01:51.101891: val_loss -0.7433 
2024-03-31 15:01:51.102100: Pseudo dice [0.8721] 
2024-03-31 15:01:51.102301: Epoch time: 103.25 s 
2024-03-31 15:01:53.149558:  
2024-03-31 15:01:53.149759: Epoch 943 
2024-03-31 15:01:53.149851: Current learning rate: 0.00076 
2024-03-31 15:03:34.686294: train_loss -0.7079 
2024-03-31 15:03:34.686657: val_loss -0.7392 
2024-03-31 15:03:34.686770: Pseudo dice [0.8795] 
2024-03-31 15:03:34.686885: Epoch time: 101.54 s 
2024-03-31 15:03:36.403418:  
2024-03-31 15:03:36.403549: Epoch 944 
2024-03-31 15:03:36.403641: Current learning rate: 0.00075 
2024-03-31 15:05:09.746003: train_loss -0.7001 
2024-03-31 15:05:09.757684: val_loss -0.7229 
2024-03-31 15:05:09.757817: Pseudo dice [0.8851] 
2024-03-31 15:05:09.757898: Epoch time: 93.34 s 
2024-03-31 15:05:09.757951: Yayy! New best EMA pseudo Dice: 0.8738 
2024-03-31 15:05:13.441079:  
2024-03-31 15:05:13.441342: Epoch 945 
2024-03-31 15:05:13.441557: Current learning rate: 0.00074 
2024-03-31 15:06:55.694610: train_loss -0.7274 
2024-03-31 15:06:55.709986: val_loss -0.7157 
2024-03-31 15:06:55.710126: Pseudo dice [0.8726] 
2024-03-31 15:06:55.710214: Epoch time: 102.25 s 
2024-03-31 15:06:57.195001:  
2024-03-31 15:06:57.195137: Epoch 946 
2024-03-31 15:06:57.195210: Current learning rate: 0.00072 
2024-03-31 15:08:35.108254: train_loss -0.717 
2024-03-31 15:08:35.136754: val_loss -0.7352 
2024-03-31 15:08:35.136897: Pseudo dice [0.8738] 
2024-03-31 15:08:35.136991: Epoch time: 97.91 s 
2024-03-31 15:08:37.900084:  
2024-03-31 15:08:37.900399: Epoch 947 
2024-03-31 15:08:37.900512: Current learning rate: 0.00071 
2024-03-31 15:10:14.320100: train_loss -0.7281 
2024-03-31 15:10:14.320402: val_loss -0.7391 
2024-03-31 15:10:14.320495: Pseudo dice [0.874] 
2024-03-31 15:10:14.320584: Epoch time: 96.42 s 
2024-03-31 15:10:17.174018:  
2024-03-31 15:10:17.174187: Epoch 948 
2024-03-31 15:10:17.174285: Current learning rate: 0.0007 
2024-03-31 15:12:08.122507: train_loss -0.7272 
2024-03-31 15:12:08.139324: val_loss -0.7389 
2024-03-31 15:12:08.139456: Pseudo dice [0.8963] 
2024-03-31 15:12:08.139548: Epoch time: 110.95 s 
2024-03-31 15:12:08.139607: Yayy! New best EMA pseudo Dice: 0.876 
2024-03-31 15:12:11.149266:  
2024-03-31 15:12:11.149496: Epoch 949 
2024-03-31 15:12:11.149607: Current learning rate: 0.00069 
2024-03-31 15:13:56.378686: train_loss -0.7205 
2024-03-31 15:13:56.405550: val_loss -0.7095 
2024-03-31 15:13:56.405747: Pseudo dice [0.8634] 
2024-03-31 15:13:56.405863: Epoch time: 105.23 s 
2024-03-31 15:14:00.396947:  
2024-03-31 15:14:00.397482: Epoch 950 
2024-03-31 15:14:00.397671: Current learning rate: 0.00067 
2024-03-31 15:15:42.330078: train_loss -0.7199 
2024-03-31 15:15:42.357609: val_loss -0.7316 
2024-03-31 15:15:42.357762: Pseudo dice [0.8764] 
2024-03-31 15:15:42.357867: Epoch time: 101.93 s 
2024-03-31 15:15:45.053901:  
2024-03-31 15:15:45.054375: Epoch 951 
2024-03-31 15:15:45.054553: Current learning rate: 0.00066 
2024-03-31 15:17:25.881594: train_loss -0.7154 
2024-03-31 15:17:25.881882: val_loss -0.7496 
2024-03-31 15:17:25.881960: Pseudo dice [0.8821] 
2024-03-31 15:17:25.882043: Epoch time: 100.83 s 
2024-03-31 15:17:27.606830:  
2024-03-31 15:17:27.607186: Epoch 952 
2024-03-31 15:17:27.607409: Current learning rate: 0.00065 
2024-03-31 15:18:55.680083: train_loss -0.7145 
2024-03-31 15:18:55.696320: val_loss -0.7332 
2024-03-31 15:18:55.696460: Pseudo dice [0.8671] 
2024-03-31 15:18:55.696541: Epoch time: 88.07 s 
2024-03-31 15:18:57.240317:  
2024-03-31 15:18:57.240487: Epoch 953 
2024-03-31 15:18:57.240687: Current learning rate: 0.00064 
2024-03-31 15:20:53.098350: train_loss -0.7145 
2024-03-31 15:20:53.113416: val_loss -0.7414 
2024-03-31 15:20:53.113761: Pseudo dice [0.883] 
2024-03-31 15:20:53.114060: Epoch time: 115.86 s 
2024-03-31 15:20:55.111521:  
2024-03-31 15:20:55.111781: Epoch 954 
2024-03-31 15:20:55.111868: Current learning rate: 0.00063 
2024-03-31 15:25:45.179103: train_loss -0.7136 
2024-03-31 15:25:45.179620: val_loss -0.7333 
2024-03-31 15:25:45.179757: Pseudo dice [0.8657] 
2024-03-31 15:25:45.179926: Epoch time: 290.07 s 
2024-03-31 15:25:53.676977:  
2024-03-31 15:25:53.677191: Epoch 955 
2024-03-31 15:25:53.677321: Current learning rate: 0.00061 
2024-03-31 15:29:16.424680: train_loss -0.7241 
2024-03-31 15:29:16.449791: val_loss -0.7297 
2024-03-31 15:29:16.449928: Pseudo dice [0.8607] 
2024-03-31 15:29:16.450019: Epoch time: 202.75 s 
2024-03-31 15:29:18.120146:  
2024-03-31 15:29:18.120299: Epoch 956 
2024-03-31 15:29:18.120377: Current learning rate: 0.0006 
2024-03-31 15:30:58.034001: train_loss -0.7283 
2024-03-31 15:30:58.049286: val_loss -0.7718 
2024-03-31 15:30:58.049665: Pseudo dice [0.8662] 
2024-03-31 15:30:58.049798: Epoch time: 99.91 s 
2024-03-31 15:30:59.789647:  
2024-03-31 15:30:59.789853: Epoch 957 
2024-03-31 15:30:59.789950: Current learning rate: 0.00059 
2024-03-31 15:32:39.136582: train_loss -0.7291 
2024-03-31 15:32:39.136930: val_loss -0.7222 
2024-03-31 15:32:39.137009: Pseudo dice [0.8586] 
2024-03-31 15:32:39.137090: Epoch time: 99.35 s 
2024-03-31 15:32:41.144141:  
2024-03-31 15:32:41.144344: Epoch 958 
2024-03-31 15:32:41.144433: Current learning rate: 0.00058 
2024-03-31 15:34:10.753285: train_loss -0.7191 
2024-03-31 15:34:10.790448: val_loss -0.7409 
2024-03-31 15:34:10.790592: Pseudo dice [0.8703] 
2024-03-31 15:34:10.790681: Epoch time: 89.61 s 
2024-03-31 15:34:13.421825:  
2024-03-31 15:34:13.422059: Epoch 959 
2024-03-31 15:34:13.422234: Current learning rate: 0.00056 
2024-03-31 15:35:46.469481: train_loss -0.7242 
2024-03-31 15:35:46.500453: val_loss -0.7471 
2024-03-31 15:35:46.500590: Pseudo dice [0.8951] 
2024-03-31 15:35:46.500680: Epoch time: 93.05 s 
2024-03-31 15:35:48.073907:  
2024-03-31 15:35:48.074100: Epoch 960 
2024-03-31 15:35:48.074198: Current learning rate: 0.00055 
2024-03-31 15:37:16.599791: train_loss -0.7447 
2024-03-31 15:37:16.610209: val_loss -0.7483 
2024-03-31 15:37:16.610308: Pseudo dice [0.8682] 
2024-03-31 15:37:16.610375: Epoch time: 88.53 s 
2024-03-31 15:37:18.185831:  
2024-03-31 15:37:18.186062: Epoch 961 
2024-03-31 15:37:18.186276: Current learning rate: 0.00054 
2024-03-31 15:39:09.339271: train_loss -0.7127 
2024-03-31 15:39:09.353476: val_loss -0.7713 
2024-03-31 15:39:09.353612: Pseudo dice [0.8757] 
2024-03-31 15:39:09.353697: Epoch time: 111.15 s 
2024-03-31 15:39:11.217918:  
2024-03-31 15:39:11.218049: Epoch 962 
2024-03-31 15:39:11.218138: Current learning rate: 0.00053 
2024-03-31 15:40:53.376411: train_loss -0.7231 
2024-03-31 15:40:53.388015: val_loss -0.7265 
2024-03-31 15:40:53.388158: Pseudo dice [0.8722] 
2024-03-31 15:40:53.388251: Epoch time: 102.16 s 
2024-03-31 15:40:55.509204:  
2024-03-31 15:40:55.509449: Epoch 963 
2024-03-31 15:40:55.509569: Current learning rate: 0.00051 
2024-03-31 15:42:24.522467: train_loss -0.7218 
2024-03-31 15:42:24.522767: val_loss -0.7624 
2024-03-31 15:42:24.522853: Pseudo dice [0.8898] 
2024-03-31 15:42:24.522932: Epoch time: 89.01 s 
2024-03-31 15:42:26.303658:  
2024-03-31 15:42:26.303985: Epoch 964 
2024-03-31 15:42:26.304122: Current learning rate: 0.0005 
2024-03-31 15:43:49.342649: train_loss -0.7183 
2024-03-31 15:43:49.356658: val_loss -0.7325 
2024-03-31 15:43:49.356839: Pseudo dice [0.8822] 
2024-03-31 15:43:49.356971: Epoch time: 83.04 s 
2024-03-31 15:43:51.508249:  
2024-03-31 15:43:51.508483: Epoch 965 
2024-03-31 15:43:51.508627: Current learning rate: 0.00049 
2024-03-31 15:45:21.472664: train_loss -0.73 
2024-03-31 15:45:21.490723: val_loss -0.7008 
2024-03-31 15:45:21.490938: Pseudo dice [0.8683] 
2024-03-31 15:45:21.491093: Epoch time: 89.97 s 
2024-03-31 15:45:23.259520:  
2024-03-31 15:45:23.259716: Epoch 966 
2024-03-31 15:45:23.259805: Current learning rate: 0.00048 
2024-03-31 15:46:59.139868: train_loss -0.7417 
2024-03-31 15:46:59.158131: val_loss -0.7868 
2024-03-31 15:46:59.158291: Pseudo dice [0.8938] 
2024-03-31 15:46:59.158395: Epoch time: 95.88 s 
2024-03-31 15:46:59.158464: Yayy! New best EMA pseudo Dice: 0.8767 
2024-03-31 15:47:03.002188:  
2024-03-31 15:47:03.002386: Epoch 967 
2024-03-31 15:47:03.002525: Current learning rate: 0.00046 
2024-03-31 15:48:56.089017: train_loss -0.7381 
2024-03-31 15:48:56.117371: val_loss -0.7377 
2024-03-31 15:48:56.117537: Pseudo dice [0.8801] 
2024-03-31 15:48:56.117651: Epoch time: 113.09 s 
2024-03-31 15:48:56.117725: Yayy! New best EMA pseudo Dice: 0.877 
2024-03-31 15:49:01.186091:  
2024-03-31 15:49:01.186379: Epoch 968 
2024-03-31 15:49:01.186580: Current learning rate: 0.00045 
2024-03-31 15:50:40.922257: train_loss -0.7409 
2024-03-31 15:50:40.951340: val_loss -0.7599 
2024-03-31 15:50:40.951483: Pseudo dice [0.884] 
2024-03-31 15:50:40.951569: Epoch time: 99.74 s 
2024-03-31 15:50:40.951644: Yayy! New best EMA pseudo Dice: 0.8777 
2024-03-31 15:50:44.100071:  
2024-03-31 15:50:44.100216: Epoch 969 
2024-03-31 15:50:44.100294: Current learning rate: 0.00044 
2024-03-31 15:52:25.828858: train_loss -0.7253 
2024-03-31 15:52:25.843834: val_loss -0.7518 
2024-03-31 15:52:25.844064: Pseudo dice [0.8797] 
2024-03-31 15:52:25.844256: Epoch time: 101.73 s 
2024-03-31 15:52:25.844388: Yayy! New best EMA pseudo Dice: 0.8779 
2024-03-31 15:52:30.099100:  
2024-03-31 15:52:30.099601: Epoch 970 
2024-03-31 15:52:30.099765: Current learning rate: 0.00043 
2024-03-31 15:54:08.016870: train_loss -0.7356 
2024-03-31 15:54:08.061042: val_loss -0.7341 
2024-03-31 15:54:08.061236: Pseudo dice [0.876] 
2024-03-31 15:54:08.061357: Epoch time: 97.92 s 
2024-03-31 15:54:10.279460:  
2024-03-31 15:54:10.279758: Epoch 971 
2024-03-31 15:54:10.279870: Current learning rate: 0.00041 
2024-03-31 15:55:54.606990: train_loss -0.7403 
2024-03-31 15:55:54.661595: val_loss -0.7227 
2024-03-31 15:55:54.661743: Pseudo dice [0.8732] 
2024-03-31 15:55:54.661837: Epoch time: 104.33 s 
2024-03-31 15:55:56.173472:  
2024-03-31 15:55:56.173594: Epoch 972 
2024-03-31 15:55:56.173670: Current learning rate: 0.0004 
2024-03-31 15:57:47.685948: train_loss -0.7188 
2024-03-31 15:57:47.697418: val_loss -0.7472 
2024-03-31 15:57:47.697778: Pseudo dice [0.8901] 
2024-03-31 15:57:47.698040: Epoch time: 111.51 s 
2024-03-31 15:57:47.698210: Yayy! New best EMA pseudo Dice: 0.8786 
2024-03-31 15:57:51.942470:  
2024-03-31 15:57:51.942677: Epoch 973 
2024-03-31 15:57:51.942837: Current learning rate: 0.00039 
2024-03-31 15:59:44.258024: train_loss -0.7261 
2024-03-31 15:59:44.296214: val_loss -0.7651 
2024-03-31 15:59:44.296379: Pseudo dice [0.8899] 
2024-03-31 15:59:44.296495: Epoch time: 112.32 s 
2024-03-31 15:59:44.300300: Yayy! New best EMA pseudo Dice: 0.8797 
2024-03-31 15:59:49.604009:  
2024-03-31 15:59:49.604231: Epoch 974 
2024-03-31 15:59:49.604398: Current learning rate: 0.00037 
2024-03-31 16:01:48.811140: train_loss -0.7455 
2024-03-31 16:01:48.822013: val_loss -0.7461 
2024-03-31 16:01:48.822272: Pseudo dice [0.8646] 
2024-03-31 16:01:48.822408: Epoch time: 119.21 s 
2024-03-31 16:01:51.410471:  
2024-03-31 16:01:51.410727: Epoch 975 
2024-03-31 16:01:51.410901: Current learning rate: 0.00036 
2024-03-31 16:03:36.502210: train_loss -0.7493 
2024-03-31 16:03:36.523255: val_loss -0.7394 
2024-03-31 16:03:36.523481: Pseudo dice [0.8664] 
2024-03-31 16:03:36.523721: Epoch time: 105.09 s 
2024-03-31 16:03:39.692261:  
2024-03-31 16:03:39.692446: Epoch 976 
2024-03-31 16:03:39.692547: Current learning rate: 0.00035 
2024-03-31 16:05:26.405633: train_loss -0.734 
2024-03-31 16:05:26.438745: val_loss -0.7488 
2024-03-31 16:05:26.438884: Pseudo dice [0.8701] 
2024-03-31 16:05:26.438973: Epoch time: 106.71 s 
2024-03-31 16:05:28.722817:  
2024-03-31 16:05:28.723139: Epoch 977 
2024-03-31 16:05:28.723407: Current learning rate: 0.00034 
2024-03-31 16:06:57.107026: train_loss -0.7353 
2024-03-31 16:06:57.107310: val_loss -0.7274 
2024-03-31 16:06:57.107383: Pseudo dice [0.8697] 
2024-03-31 16:06:57.107460: Epoch time: 88.39 s 
2024-03-31 16:06:58.672953:  
2024-03-31 16:06:58.673226: Epoch 978 
2024-03-31 16:06:58.673398: Current learning rate: 0.00032 
2024-03-31 16:08:28.574473: train_loss -0.7264 
2024-03-31 16:08:28.655737: val_loss -0.752 
2024-03-31 16:08:28.656616: Pseudo dice [0.8853] 
2024-03-31 16:08:28.656735: Epoch time: 89.9 s 
2024-03-31 16:08:30.382198:  
2024-03-31 16:08:30.382327: Epoch 979 
2024-03-31 16:08:30.382401: Current learning rate: 0.00031 
2024-03-31 16:09:56.315145: train_loss -0.7242 
2024-03-31 16:09:56.315451: val_loss -0.7427 
2024-03-31 16:09:56.315527: Pseudo dice [0.8883] 
2024-03-31 16:09:56.315624: Epoch time: 85.93 s 
2024-03-31 16:09:58.228646:  
2024-03-31 16:09:58.228929: Epoch 980 
2024-03-31 16:09:58.229020: Current learning rate: 0.0003 
2024-03-31 16:11:22.403363: train_loss -0.7229 
2024-03-31 16:11:22.497890: val_loss -0.7182 
2024-03-31 16:11:22.498055: Pseudo dice [0.8686] 
2024-03-31 16:11:22.498157: Epoch time: 84.18 s 
2024-03-31 16:11:24.305574:  
2024-03-31 16:11:24.305872: Epoch 981 
2024-03-31 16:11:24.305984: Current learning rate: 0.00028 
2024-03-31 16:12:52.508731: train_loss -0.743 
2024-03-31 16:12:52.523111: val_loss -0.7318 
2024-03-31 16:12:52.523231: Pseudo dice [0.8698] 
2024-03-31 16:12:52.523314: Epoch time: 88.2 s 
2024-03-31 16:12:54.463906:  
2024-03-31 16:12:54.464064: Epoch 982 
2024-03-31 16:12:54.464157: Current learning rate: 0.00027 
2024-03-31 16:14:17.030680: train_loss -0.733 
2024-03-31 16:14:17.048604: val_loss -0.7777 
2024-03-31 16:14:17.048792: Pseudo dice [0.888] 
2024-03-31 16:14:17.048910: Epoch time: 82.57 s 
2024-03-31 16:14:19.423410:  
2024-03-31 16:14:19.423647: Epoch 983 
2024-03-31 16:14:19.423781: Current learning rate: 0.00026 
2024-03-31 16:15:50.418147: train_loss -0.7294 
2024-03-31 16:15:50.440366: val_loss -0.7062 
2024-03-31 16:15:50.440542: Pseudo dice [0.8649] 
2024-03-31 16:15:50.440658: Epoch time: 91.0 s 
2024-03-31 16:15:52.098831:  
2024-03-31 16:15:52.098971: Epoch 984 
2024-03-31 16:15:52.099061: Current learning rate: 0.00024 
2024-03-31 16:17:22.531712: train_loss -0.7356 
2024-03-31 16:17:22.545418: val_loss -0.7552 
2024-03-31 16:17:22.545527: Pseudo dice [0.8806] 
2024-03-31 16:17:22.545610: Epoch time: 90.43 s 
2024-03-31 16:17:24.261944:  
2024-03-31 16:17:24.262111: Epoch 985 
2024-03-31 16:17:24.262197: Current learning rate: 0.00023 
2024-03-31 16:18:51.076775: train_loss -0.7321 
2024-03-31 16:18:51.091021: val_loss -0.7404 
2024-03-31 16:18:51.091230: Pseudo dice [0.8674] 
2024-03-31 16:18:51.091389: Epoch time: 86.82 s 
2024-03-31 16:18:53.526729:  
2024-03-31 16:18:53.527048: Epoch 986 
2024-03-31 16:18:53.527224: Current learning rate: 0.00021 
2024-03-31 16:20:21.033231: train_loss -0.7284 
2024-03-31 16:20:21.033493: val_loss -0.7512 
2024-03-31 16:20:21.033562: Pseudo dice [0.8857] 
2024-03-31 16:20:21.033634: Epoch time: 87.51 s 
2024-03-31 16:20:23.301106:  
2024-03-31 16:20:23.301288: Epoch 987 
2024-03-31 16:20:23.301375: Current learning rate: 0.0002 
2024-03-31 16:21:54.098981: train_loss -0.7487 
2024-03-31 16:21:54.133319: val_loss -0.746 
2024-03-31 16:21:54.133564: Pseudo dice [0.8697] 
2024-03-31 16:21:54.133670: Epoch time: 90.8 s 
2024-03-31 16:21:56.237946:  
2024-03-31 16:21:56.238106: Epoch 988 
2024-03-31 16:21:56.238190: Current learning rate: 0.00019 
2024-03-31 16:23:24.360283: train_loss -0.7256 
2024-03-31 16:23:24.375404: val_loss -0.7542 
2024-03-31 16:23:24.375865: Pseudo dice [0.8793] 
2024-03-31 16:23:24.376019: Epoch time: 88.12 s 
2024-03-31 16:23:26.805033:  
2024-03-31 16:23:26.805241: Epoch 989 
2024-03-31 16:23:26.805375: Current learning rate: 0.00017 
2024-03-31 16:24:55.603736: train_loss -0.7392 
2024-03-31 16:24:55.604183: val_loss -0.7745 
2024-03-31 16:24:55.604301: Pseudo dice [0.8848] 
2024-03-31 16:24:55.604386: Epoch time: 88.8 s 
2024-03-31 16:24:57.714877:  
2024-03-31 16:24:57.720883: Epoch 990 
2024-03-31 16:24:57.721283: Current learning rate: 0.00016 
2024-03-31 16:26:25.656754: train_loss -0.7266 
2024-03-31 16:26:25.671965: val_loss -0.726 
2024-03-31 16:26:25.672160: Pseudo dice [0.8616] 
2024-03-31 16:26:25.672277: Epoch time: 87.94 s 
2024-03-31 16:26:27.428220:  
2024-03-31 16:26:27.428506: Epoch 991 
2024-03-31 16:26:27.428701: Current learning rate: 0.00014 
2024-03-31 16:27:54.671048: train_loss -0.7332 
2024-03-31 16:27:54.684608: val_loss -0.7531 
2024-03-31 16:27:54.684754: Pseudo dice [0.8873] 
2024-03-31 16:27:54.684845: Epoch time: 87.24 s 
2024-03-31 16:27:57.134831:  
2024-03-31 16:27:57.135179: Epoch 992 
2024-03-31 16:27:57.135386: Current learning rate: 0.00013 
2024-03-31 16:29:24.928064: train_loss -0.7316 
2024-03-31 16:29:24.928453: val_loss -0.7296 
2024-03-31 16:29:24.928560: Pseudo dice [0.8647] 
2024-03-31 16:29:24.928706: Epoch time: 87.79 s 
2024-03-31 16:29:27.354861:  
2024-03-31 16:29:27.355119: Epoch 993 
2024-03-31 16:29:27.355271: Current learning rate: 0.00011 
2024-03-31 16:30:56.336737: train_loss -0.736 
2024-03-31 16:30:56.368582: val_loss -0.7383 
2024-03-31 16:30:56.368719: Pseudo dice [0.8874] 
2024-03-31 16:30:56.368802: Epoch time: 88.98 s 
2024-03-31 16:30:57.602128:  
2024-03-31 16:30:57.602234: Epoch 994 
2024-03-31 16:30:57.602308: Current learning rate: 0.0001 
2024-03-31 16:32:21.984564: train_loss -0.7338 
2024-03-31 16:32:22.018884: val_loss -0.7697 
2024-03-31 16:32:22.019748: Pseudo dice [0.8918] 
2024-03-31 16:32:22.019857: Epoch time: 84.38 s 
2024-03-31 16:32:24.043505:  
2024-03-31 16:32:24.043676: Epoch 995 
2024-03-31 16:32:24.043823: Current learning rate: 8e-05 
2024-03-31 16:33:52.455599: train_loss -0.7431 
2024-03-31 16:33:52.665268: val_loss -0.7553 
2024-03-31 16:33:52.665421: Pseudo dice [0.8636] 
2024-03-31 16:33:52.665518: Epoch time: 88.41 s 
2024-03-31 16:33:55.686661:  
2024-03-31 16:33:55.686793: Epoch 996 
2024-03-31 16:33:55.686876: Current learning rate: 7e-05 
2024-03-31 16:35:20.775481: train_loss -0.7341 
2024-03-31 16:35:20.775784: val_loss -0.7308 
2024-03-31 16:35:20.775860: Pseudo dice [0.8923] 
2024-03-31 16:35:20.775940: Epoch time: 85.09 s 
2024-03-31 16:35:22.931458:  
2024-03-31 16:35:22.931646: Epoch 997 
2024-03-31 16:35:22.931733: Current learning rate: 5e-05 
2024-03-31 16:36:49.668564: train_loss -0.7366 
2024-03-31 16:36:49.684897: val_loss -0.7324 
2024-03-31 16:36:49.685051: Pseudo dice [0.8837] 
2024-03-31 16:36:49.685170: Epoch time: 86.74 s 
2024-03-31 16:36:51.065921:  
2024-03-31 16:36:51.066111: Epoch 998 
2024-03-31 16:36:51.066291: Current learning rate: 4e-05 
2024-03-31 16:38:17.196182: train_loss -0.7339 
2024-03-31 16:38:17.215556: val_loss -0.7275 
2024-03-31 16:38:17.215677: Pseudo dice [0.8689] 
2024-03-31 16:38:17.215759: Epoch time: 86.13 s 
2024-03-31 16:38:19.139294:  
2024-03-31 16:38:19.139414: Epoch 999 
2024-03-31 16:38:19.139485: Current learning rate: 2e-05 
2024-03-31 16:39:44.914760: train_loss -0.727 
2024-03-31 16:39:44.930037: val_loss -0.736 
2024-03-31 16:39:44.930194: Pseudo dice [0.873] 
2024-03-31 16:39:44.930283: Epoch time: 85.78 s 
2024-03-31 16:39:47.629753: Training done. 
2024-03-31 16:39:47.962223: predicting ATLAS_r001s001 
2024-03-31 16:39:48.122213: ATLAS_r001s001, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:39:54.074217: predicting ATLAS_r001s002 
2024-03-31 16:39:54.076131: ATLAS_r001s002, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:39:56.474045: predicting ATLAS_r001s003 
2024-03-31 16:39:56.475335: ATLAS_r001s003, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:39:58.886836: predicting ATLAS_r001s004 
2024-03-31 16:39:58.902555: ATLAS_r001s004, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:40:01.344020: predicting ATLAS_r001s005 
2024-03-31 16:40:01.345407: ATLAS_r001s005, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:40:03.809250: predicting ATLAS_r001s006 
2024-03-31 16:40:03.853815: ATLAS_r001s006, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:40:06.349863: predicting ATLAS_r001s007 
2024-03-31 16:40:06.351413: ATLAS_r001s007, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:40:08.883400: predicting ATLAS_r001s008 
2024-03-31 16:40:08.885636: ATLAS_r001s008, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:40:11.391701: predicting ATLAS_r001s009 
2024-03-31 16:40:11.393447: ATLAS_r001s009, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:40:13.966110: predicting ATLAS_r001s010 
2024-03-31 16:40:13.980273: ATLAS_r001s010, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:40:17.201237: predicting ATLAS_r001s011 
2024-03-31 16:40:17.202798: ATLAS_r001s011, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:40:19.723767: predicting ATLAS_r001s012 
2024-03-31 16:40:19.725669: ATLAS_r001s012, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:40:22.302186: predicting ATLAS_r001s013 
2024-03-31 16:40:22.304031: ATLAS_r001s013, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:40:24.862079: predicting ATLAS_r001s014 
2024-03-31 16:40:24.863878: ATLAS_r001s014, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:40:27.440577: predicting ATLAS_r001s015 
2024-03-31 16:40:27.442362: ATLAS_r001s015, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:40:30.018698: predicting ATLAS_r001s016 
2024-03-31 16:40:30.035390: ATLAS_r001s016, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:40:32.609310: predicting ATLAS_r001s017 
2024-03-31 16:40:32.611746: ATLAS_r001s017, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:40:35.185626: predicting ATLAS_r001s018 
2024-03-31 16:40:35.188549: ATLAS_r001s018, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:40:37.757194: predicting ATLAS_r001s019 
2024-03-31 16:40:37.759214: ATLAS_r001s019, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:40:40.345382: predicting ATLAS_r001s020 
2024-03-31 16:40:40.349123: ATLAS_r001s020, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:40:42.938108: predicting ATLAS_r001s021 
2024-03-31 16:40:42.954956: ATLAS_r001s021, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:40:45.598195: predicting ATLAS_r001s022 
2024-03-31 16:40:45.600981: ATLAS_r001s022, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 16:40:48.281639: predicting ATLAS_r001s023 
2024-03-31 16:40:48.283521: ATLAS_r001s023, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:40:50.857093: predicting ATLAS_r001s024 
2024-03-31 16:40:50.867949: ATLAS_r001s024, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:40:53.560488: predicting ATLAS_r001s025 
2024-03-31 16:40:53.562896: ATLAS_r001s025, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:40:56.138877: predicting ATLAS_r001s026 
2024-03-31 16:40:56.141171: ATLAS_r001s026, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:40:58.678584: predicting ATLAS_r001s027 
2024-03-31 16:40:58.694991: ATLAS_r001s027, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:41:01.352066: predicting ATLAS_r001s028 
2024-03-31 16:41:01.354614: ATLAS_r001s028, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:03.928077: predicting ATLAS_r001s029 
2024-03-31 16:41:03.941977: ATLAS_r001s029, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:06.471772: predicting ATLAS_r001s030 
2024-03-31 16:41:06.475595: ATLAS_r001s030, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:09.050714: predicting ATLAS_r001s031 
2024-03-31 16:41:09.054010: ATLAS_r001s031, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:11.605332: predicting ATLAS_r001s032 
2024-03-31 16:41:11.607632: ATLAS_r001s032, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:14.118830: predicting ATLAS_r001s033 
2024-03-31 16:41:14.121287: ATLAS_r001s033, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:16.659283: predicting ATLAS_r001s034 
2024-03-31 16:41:16.686517: ATLAS_r001s034, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:19.266693: predicting ATLAS_r001s036 
2024-03-31 16:41:19.269722: ATLAS_r001s036, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:21.767467: predicting ATLAS_r001s037 
2024-03-31 16:41:21.769603: ATLAS_r001s037, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:24.261403: predicting ATLAS_r001s038 
2024-03-31 16:41:24.263437: ATLAS_r001s038, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:26.799278: predicting ATLAS_r001s039 
2024-03-31 16:41:26.806740: ATLAS_r001s039, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:29.289202: predicting ATLAS_r002s001 
2024-03-31 16:41:29.292493: ATLAS_r002s001, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:41:31.765950: predicting ATLAS_r002s002 
2024-03-31 16:41:31.767498: ATLAS_r002s002, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:41:34.239230: predicting ATLAS_r002s003 
2024-03-31 16:41:34.247833: ATLAS_r002s003, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:36.699656: predicting ATLAS_r002s004 
2024-03-31 16:41:36.701180: ATLAS_r002s004, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:39.232241: predicting ATLAS_r002s005 
2024-03-31 16:41:39.245455: ATLAS_r002s005, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:41.794465: predicting ATLAS_r002s006 
2024-03-31 16:41:41.833869: ATLAS_r002s006, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:44.338528: predicting ATLAS_r002s007 
2024-03-31 16:41:44.340629: ATLAS_r002s007, shape torch.Size([1, 190, 229, 193]), rank 0 
2024-03-31 16:41:48.003173: predicting ATLAS_r002s008 
2024-03-31 16:41:48.006311: ATLAS_r002s008, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:50.449068: predicting ATLAS_r002s009 
2024-03-31 16:41:50.459325: ATLAS_r002s009, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:52.978817: predicting ATLAS_r002s010 
2024-03-31 16:41:52.980738: ATLAS_r002s010, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:41:55.422098: predicting ATLAS_r002s011 
2024-03-31 16:41:55.424409: ATLAS_r002s011, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:41:57.852740: predicting ATLAS_r002s012 
2024-03-31 16:41:57.855910: ATLAS_r002s012, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:42:00.277178: predicting ATLAS_r003s001 
2024-03-31 16:42:00.278910: ATLAS_r003s001, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:42:02.695478: predicting ATLAS_r003s002 
2024-03-31 16:42:02.735001: ATLAS_r003s002, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:42:05.274793: predicting ATLAS_r003s003 
2024-03-31 16:42:05.276613: ATLAS_r003s003, shape torch.Size([1, 193, 229, 193]), rank 0 
2024-03-31 16:42:10.793602: predicting ATLAS_r003s004 
2024-03-31 16:42:10.809543: ATLAS_r003s004, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:42:13.215568: predicting ATLAS_r003s005 
2024-03-31 16:42:13.256463: ATLAS_r003s005, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:42:15.769765: predicting ATLAS_r003s006 
2024-03-31 16:42:15.780373: ATLAS_r003s006, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:42:18.269227: predicting ATLAS_r003s007 
2024-03-31 16:42:18.271118: ATLAS_r003s007, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:42:20.669947: predicting ATLAS_r003s008 
2024-03-31 16:42:20.688309: ATLAS_r003s008, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:42:23.141696: predicting ATLAS_r003s009 
2024-03-31 16:42:23.144975: ATLAS_r003s009, shape torch.Size([1, 189, 229, 193]), rank 0 
2024-03-31 16:42:26.752863: predicting ATLAS_r003s010 
2024-03-31 16:42:26.755825: ATLAS_r003s010, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:42:29.146585: predicting ATLAS_r003s011 
2024-03-31 16:42:29.175543: ATLAS_r003s011, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:42:31.631754: predicting ATLAS_r003s012 
2024-03-31 16:42:31.633634: ATLAS_r003s012, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:42:34.061475: predicting ATLAS_r003s013 
2024-03-31 16:42:34.063296: ATLAS_r003s013, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:42:36.447604: predicting ATLAS_r003s014 
2024-03-31 16:42:36.449419: ATLAS_r003s014, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:42:38.844824: predicting ATLAS_r003s015 
2024-03-31 16:42:38.847933: ATLAS_r003s015, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:42:41.243964: predicting ATLAS_r004s001 
2024-03-31 16:42:41.245845: ATLAS_r004s001, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:42:43.633551: predicting ATLAS_r004s002 
2024-03-31 16:42:43.648829: ATLAS_r004s002, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:42:46.164416: predicting ATLAS_r004s003 
2024-03-31 16:42:46.166347: ATLAS_r004s003, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:42:48.579458: predicting ATLAS_r004s004 
2024-03-31 16:42:48.607814: ATLAS_r004s004, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:42:51.174608: predicting ATLAS_r004s005 
2024-03-31 16:42:51.199159: ATLAS_r004s005, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:42:53.582046: predicting ATLAS_r004s006 
2024-03-31 16:42:53.583822: ATLAS_r004s006, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:42:55.964239: predicting ATLAS_r004s007 
2024-03-31 16:42:56.002213: ATLAS_r004s007, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:42:58.489768: predicting ATLAS_r004s008 
2024-03-31 16:42:58.491568: ATLAS_r004s008, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:43:00.872503: predicting ATLAS_r004s009 
2024-03-31 16:43:00.874694: ATLAS_r004s009, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:43:03.254297: predicting ATLAS_r004s010 
2024-03-31 16:43:03.257358: ATLAS_r004s010, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:05.633018: predicting ATLAS_r004s011 
2024-03-31 16:43:05.647302: ATLAS_r004s011, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:08.122084: predicting ATLAS_r004s012 
2024-03-31 16:43:08.123509: ATLAS_r004s012, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:10.539458: predicting ATLAS_r004s013 
2024-03-31 16:43:10.570936: ATLAS_r004s013, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:13.041860: predicting ATLAS_r004s014 
2024-03-31 16:43:13.044626: ATLAS_r004s014, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:15.415238: predicting ATLAS_r004s015 
2024-03-31 16:43:15.443230: ATLAS_r004s015, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:17.959913: predicting ATLAS_r004s016 
2024-03-31 16:43:17.961851: ATLAS_r004s016, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:20.332030: predicting ATLAS_r004s017 
2024-03-31 16:43:20.333899: ATLAS_r004s017, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:22.698570: predicting ATLAS_r004s018 
2024-03-31 16:43:22.700645: ATLAS_r004s018, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:43:25.085329: predicting ATLAS_r004s019 
2024-03-31 16:43:25.087389: ATLAS_r004s019, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:43:27.460972: predicting ATLAS_r004s020 
2024-03-31 16:43:27.498309: ATLAS_r004s020, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:29.944871: predicting ATLAS_r004s021 
2024-03-31 16:43:29.946684: ATLAS_r004s021, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:32.402416: predicting ATLAS_r004s022 
2024-03-31 16:43:32.404017: ATLAS_r004s022, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:34.764888: predicting ATLAS_r004s023 
2024-03-31 16:43:34.767462: ATLAS_r004s023, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:43:37.134446: predicting ATLAS_r004s024 
2024-03-31 16:43:37.137304: ATLAS_r004s024, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:39.501348: predicting ATLAS_r004s025 
2024-03-31 16:43:39.503215: ATLAS_r004s025, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:41.869362: predicting ATLAS_r004s026 
2024-03-31 16:43:41.905289: ATLAS_r004s026, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:44.343704: predicting ATLAS_r004s027 
2024-03-31 16:43:44.375154: ATLAS_r004s027, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:43:46.820881: predicting ATLAS_r004s028 
2024-03-31 16:43:46.822939: ATLAS_r004s028, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:49.183358: predicting ATLAS_r004s029 
2024-03-31 16:43:49.199513: ATLAS_r004s029, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 16:43:51.591096: predicting ATLAS_r004s030 
2024-03-31 16:43:51.623111: ATLAS_r004s030, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:43:54.159706: predicting ATLAS_r004s031 
2024-03-31 16:43:54.162002: ATLAS_r004s031, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:43:56.519413: predicting ATLAS_r004s032 
2024-03-31 16:43:56.521381: ATLAS_r004s032, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:43:58.880717: predicting ATLAS_r004s033 
2024-03-31 16:43:58.882071: ATLAS_r004s033, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:44:01.263063: predicting ATLAS_r004s034 
2024-03-31 16:44:01.283482: ATLAS_r004s034, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:44:03.751375: predicting ATLAS_r004s035 
2024-03-31 16:44:03.753359: ATLAS_r004s035, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:44:06.137668: predicting ATLAS_r004s036 
2024-03-31 16:44:06.180420: ATLAS_r004s036, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:44:08.642015: predicting ATLAS_r004s037 
2024-03-31 16:44:08.644722: ATLAS_r004s037, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:44:11.002027: predicting ATLAS_r005s015 
2024-03-31 16:44:11.004736: ATLAS_r005s015, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:44:13.365177: predicting ATLAS_r005s026 
2024-03-31 16:44:13.366871: ATLAS_r005s026, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:44:15.717944: predicting ATLAS_r005s031 
2024-03-31 16:44:15.744529: ATLAS_r005s031, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:44:18.184513: predicting ATLAS_r005s045 
2024-03-31 16:44:18.186248: ATLAS_r005s045, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:44:20.540918: predicting ATLAS_r005s046 
2024-03-31 16:44:20.543746: ATLAS_r005s046, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:44:22.893165: predicting ATLAS_r005s048 
2024-03-31 16:44:22.895226: ATLAS_r005s048, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:44:25.269409: predicting ATLAS_r005s049 
2024-03-31 16:44:25.271178: ATLAS_r005s049, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:44:27.618025: predicting ATLAS_r005s055 
2024-03-31 16:44:27.619811: ATLAS_r005s055, shape torch.Size([1, 193, 229, 193]), rank 0 
2024-03-31 16:44:32.919209: predicting ATLAS_r005s058 
2024-03-31 16:44:32.923369: ATLAS_r005s058, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:44:35.376847: predicting ATLAS_r005s068 
2024-03-31 16:44:35.399965: ATLAS_r005s068, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:44:37.893108: predicting ATLAS_r005s069 
2024-03-31 16:44:37.895101: ATLAS_r005s069, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:44:40.239045: predicting ATLAS_r005s070 
2024-03-31 16:44:40.240952: ATLAS_r005s070, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:44:42.640932: predicting ATLAS_r005s073 
2024-03-31 16:44:42.642820: ATLAS_r005s073, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:44:44.992051: predicting ATLAS_r005s074 
2024-03-31 16:44:44.995021: ATLAS_r005s074, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:44:47.342683: predicting ATLAS_r005s075 
2024-03-31 16:44:47.344992: ATLAS_r005s075, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:44:49.693726: predicting ATLAS_r005s076 
2024-03-31 16:44:49.706564: ATLAS_r005s076, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:44:52.119378: predicting ATLAS_r005s077 
2024-03-31 16:44:52.121729: ATLAS_r005s077, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:44:54.497074: predicting ATLAS_r005s081 
2024-03-31 16:44:54.499863: ATLAS_r005s081, shape torch.Size([1, 193, 229, 193]), rank 0 
2024-03-31 16:44:59.771378: predicting ATLAS_r009s001 
2024-03-31 16:44:59.775345: ATLAS_r009s001, shape torch.Size([1, 187, 229, 193]), rank 0 
2024-03-31 16:45:03.302813: predicting ATLAS_r009s002 
2024-03-31 16:45:03.306576: ATLAS_r009s002, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:45:05.652854: predicting ATLAS_r009s003 
2024-03-31 16:45:05.654758: ATLAS_r009s003, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:45:08.001847: predicting ATLAS_r009s004 
2024-03-31 16:45:08.004101: ATLAS_r009s004, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:45:10.351281: predicting ATLAS_r009s005 
2024-03-31 16:45:10.353325: ATLAS_r009s005, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:45:12.696015: predicting ATLAS_r009s006 
2024-03-31 16:45:12.697865: ATLAS_r009s006, shape torch.Size([1, 150, 182, 145]), rank 0 
2024-03-31 16:45:15.155616: predicting ATLAS_r009s007 
2024-03-31 16:45:15.164608: ATLAS_r009s007, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:45:17.509751: predicting ATLAS_r009s008 
2024-03-31 16:45:17.511824: ATLAS_r009s008, shape torch.Size([1, 189, 229, 193]), rank 0 
2024-03-31 16:45:21.112051: predicting ATLAS_r009s009 
2024-03-31 16:45:21.116175: ATLAS_r009s009, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:45:23.461105: predicting ATLAS_r009s010 
2024-03-31 16:45:23.464226: ATLAS_r009s010, shape torch.Size([1, 192, 229, 193]), rank 0 
2024-03-31 16:45:27.066622: predicting ATLAS_r009s011 
2024-03-31 16:45:27.068696: ATLAS_r009s011, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:45:29.414133: predicting ATLAS_r009s012 
2024-03-31 16:45:29.417180: ATLAS_r009s012, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:45:31.771255: predicting ATLAS_r009s013 
2024-03-31 16:45:31.773414: ATLAS_r009s013, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:45:34.111725: predicting ATLAS_r009s014 
2024-03-31 16:45:34.127796: ATLAS_r009s014, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:45:36.619259: predicting ATLAS_r009s015 
2024-03-31 16:45:36.620942: ATLAS_r009s015, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:45:38.963648: predicting ATLAS_r009s016 
2024-03-31 16:45:38.966738: ATLAS_r009s016, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:45:41.308530: predicting ATLAS_r009s017 
2024-03-31 16:45:41.317091: ATLAS_r009s017, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:45:43.715847: predicting ATLAS_r009s018 
2024-03-31 16:45:43.717650: ATLAS_r009s018, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:45:46.089979: predicting ATLAS_r009s020 
2024-03-31 16:45:46.091873: ATLAS_r009s020, shape torch.Size([1, 190, 229, 193]), rank 0 
2024-03-31 16:45:49.652495: predicting ATLAS_r009s021 
2024-03-31 16:45:49.655970: ATLAS_r009s021, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:45:52.000172: predicting ATLAS_r009s022 
2024-03-31 16:45:52.002029: ATLAS_r009s022, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:45:54.340320: predicting ATLAS_r009s024 
2024-03-31 16:45:54.343036: ATLAS_r009s024, shape torch.Size([1, 150, 182, 145]), rank 0 
2024-03-31 16:45:56.697145: predicting ATLAS_r009s025 
2024-03-31 16:45:56.698390: ATLAS_r009s025, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:45:59.039129: predicting ATLAS_r009s026 
2024-03-31 16:45:59.041394: ATLAS_r009s026, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:46:01.381780: predicting ATLAS_r009s027 
2024-03-31 16:46:01.480124: ATLAS_r009s027, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:46:04.307284: predicting ATLAS_r009s028 
2024-03-31 16:46:04.310283: ATLAS_r009s028, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:46:06.754997: predicting ATLAS_r009s029 
2024-03-31 16:46:06.757090: ATLAS_r009s029, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:46:09.093354: predicting ATLAS_r009s030 
2024-03-31 16:46:09.096486: ATLAS_r009s030, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:46:11.511324: predicting ATLAS_r009s031 
2024-03-31 16:46:11.514311: ATLAS_r009s031, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:46:13.850701: predicting ATLAS_r009s032 
2024-03-31 16:46:13.852134: ATLAS_r009s032, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:46:16.191710: predicting ATLAS_r009s034 
2024-03-31 16:46:16.193097: ATLAS_r009s034, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:46:18.532835: predicting ATLAS_r009s035 
2024-03-31 16:46:18.535245: ATLAS_r009s035, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:46:20.875757: predicting ATLAS_r009s036 
2024-03-31 16:46:20.889648: ATLAS_r009s036, shape torch.Size([1, 190, 229, 193]), rank 0 
2024-03-31 16:46:24.408854: predicting ATLAS_r009s037 
2024-03-31 16:46:24.411764: ATLAS_r009s037, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:46:26.752753: predicting ATLAS_r009s038 
2024-03-31 16:46:26.754490: ATLAS_r009s038, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:46:29.094789: predicting ATLAS_r009s039 
2024-03-31 16:46:29.096930: ATLAS_r009s039, shape torch.Size([1, 150, 182, 145]), rank 0 
2024-03-31 16:46:31.441901: predicting ATLAS_r009s040 
2024-03-31 16:46:31.467961: ATLAS_r009s040, shape torch.Size([1, 193, 229, 193]), rank 0 
2024-03-31 16:46:37.042982: predicting ATLAS_r009s041 
2024-03-31 16:46:37.045367: ATLAS_r009s041, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:46:39.388300: predicting ATLAS_r009s043 
2024-03-31 16:46:39.409897: ATLAS_r009s043, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:46:41.838486: predicting ATLAS_r009s044 
2024-03-31 16:46:41.840317: ATLAS_r009s044, shape torch.Size([1, 148, 182, 145]), rank 0 
2024-03-31 16:46:44.176196: predicting ATLAS_r009s045 
2024-03-31 16:46:44.178053: ATLAS_r009s045, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:46:46.580527: predicting ATLAS_r009s046 
2024-03-31 16:46:46.582434: ATLAS_r009s046, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:46:48.922789: predicting ATLAS_r009s048 
2024-03-31 16:46:48.925870: ATLAS_r009s048, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:46:51.264330: predicting ATLAS_r009s049 
2024-03-31 16:46:51.266290: ATLAS_r009s049, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:46:53.650699: predicting ATLAS_r009s050 
2024-03-31 16:46:53.652527: ATLAS_r009s050, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:46:55.987781: predicting ATLAS_r009s051 
2024-03-31 16:46:56.008009: ATLAS_r009s051, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:46:58.342845: predicting ATLAS_r009s052 
2024-03-31 16:46:58.344770: ATLAS_r009s052, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:47:00.681574: predicting ATLAS_r009s053 
2024-03-31 16:47:00.683366: ATLAS_r009s053, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:47:03.037950: predicting ATLAS_r009s054 
2024-03-31 16:47:03.039213: ATLAS_r009s054, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:47:05.511607: predicting ATLAS_r009s055 
2024-03-31 16:47:05.513182: ATLAS_r009s055, shape torch.Size([1, 191, 229, 193]), rank 0 
2024-03-31 16:47:09.134409: predicting ATLAS_r009s056 
2024-03-31 16:47:09.136928: ATLAS_r009s056, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:47:11.607322: predicting ATLAS_r009s057 
2024-03-31 16:47:11.610180: ATLAS_r009s057, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:47:13.950041: predicting ATLAS_r009s058 
2024-03-31 16:47:13.951971: ATLAS_r009s058, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:47:16.291354: predicting ATLAS_r009s060 
2024-03-31 16:47:16.293882: ATLAS_r009s060, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:47:18.629996: predicting ATLAS_r009s061 
2024-03-31 16:47:18.658541: ATLAS_r009s061, shape torch.Size([1, 150, 182, 145]), rank 0 
2024-03-31 16:47:21.097816: predicting ATLAS_r009s062 
2024-03-31 16:47:21.100428: ATLAS_r009s062, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:47:23.472039: predicting ATLAS_r009s063 
2024-03-31 16:47:23.509191: ATLAS_r009s063, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:47:25.908216: predicting ATLAS_r009s064 
2024-03-31 16:47:25.922687: ATLAS_r009s064, shape torch.Size([1, 190, 229, 193]), rank 0 
2024-03-31 16:47:29.520205: predicting ATLAS_r009s065 
2024-03-31 16:47:29.541024: ATLAS_r009s065, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:47:31.944554: predicting ATLAS_r009s066 
2024-03-31 16:47:31.974157: ATLAS_r009s066, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:47:34.466655: predicting ATLAS_r009s067 
2024-03-31 16:47:34.485220: ATLAS_r009s067, shape torch.Size([1, 151, 179, 145]), rank 0 
2024-03-31 16:47:36.840507: predicting ATLAS_r009s071 
2024-03-31 16:47:36.842278: ATLAS_r009s071, shape torch.Size([1, 192, 229, 193]), rank 0 
2024-03-31 16:47:40.375154: predicting ATLAS_r009s072 
2024-03-31 16:47:40.390196: ATLAS_r009s072, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:47:42.835027: predicting ATLAS_r009s073 
2024-03-31 16:47:42.837311: ATLAS_r009s073, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:47:45.178855: predicting ATLAS_r009s074 
2024-03-31 16:47:45.182584: ATLAS_r009s074, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:47:47.521625: predicting ATLAS_r009s075 
2024-03-31 16:47:47.539938: ATLAS_r009s075, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:47:49.875762: predicting ATLAS_r009s076 
2024-03-31 16:47:49.889634: ATLAS_r009s076, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 16:47:52.303122: predicting ATLAS_r009s077 
2024-03-31 16:47:52.306063: ATLAS_r009s077, shape torch.Size([1, 192, 229, 193]), rank 0 
2024-03-31 16:47:55.816711: predicting ATLAS_r009s078 
2024-03-31 16:47:55.829291: ATLAS_r009s078, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:47:58.169358: predicting ATLAS_r009s079 
2024-03-31 16:47:58.171873: ATLAS_r009s079, shape torch.Size([1, 187, 229, 193]), rank 0 
2024-03-31 16:48:01.711137: predicting ATLAS_r009s082 
2024-03-31 16:48:01.713365: ATLAS_r009s082, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:48:04.281210: predicting ATLAS_r009s083 
2024-03-31 16:48:04.283313: ATLAS_r009s083, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:48:06.622479: predicting ATLAS_r009s084 
2024-03-31 16:48:06.623891: ATLAS_r009s084, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:48:08.962808: predicting ATLAS_r009s085 
2024-03-31 16:48:08.966381: ATLAS_r009s085, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:48:11.305777: predicting ATLAS_r009s086 
2024-03-31 16:48:11.308086: ATLAS_r009s086, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:48:13.674168: predicting ATLAS_r009s087 
2024-03-31 16:48:13.675838: ATLAS_r009s087, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:48:16.011333: predicting ATLAS_r009s088 
2024-03-31 16:48:16.028192: ATLAS_r009s088, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:48:18.362667: predicting ATLAS_r009s089 
2024-03-31 16:48:18.364394: ATLAS_r009s089, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:48:20.699114: predicting ATLAS_r009s090 
2024-03-31 16:48:20.700868: ATLAS_r009s090, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:48:23.037271: predicting ATLAS_r009s091 
2024-03-31 16:48:23.052098: ATLAS_r009s091, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:48:25.390000: predicting ATLAS_r009s092 
2024-03-31 16:48:25.391887: ATLAS_r009s092, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:48:27.730740: predicting ATLAS_r009s093 
2024-03-31 16:48:27.741176: ATLAS_r009s093, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:48:30.188466: predicting ATLAS_r009s094 
2024-03-31 16:48:30.190266: ATLAS_r009s094, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:48:32.531356: predicting ATLAS_r009s095 
2024-03-31 16:48:32.533066: ATLAS_r009s095, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:48:34.915586: predicting ATLAS_r009s096 
2024-03-31 16:48:34.917486: ATLAS_r009s096, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:48:37.430701: predicting ATLAS_r009s097 
2024-03-31 16:48:37.432328: ATLAS_r009s097, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:48:39.771698: predicting ATLAS_r009s098 
2024-03-31 16:48:39.793733: ATLAS_r009s098, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:48:42.221914: predicting ATLAS_r009s099 
2024-03-31 16:48:42.225103: ATLAS_r009s099, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:48:44.563039: predicting ATLAS_r009s100 
2024-03-31 16:48:44.579590: ATLAS_r009s100, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:48:46.986816: predicting ATLAS_r009s102 
2024-03-31 16:48:46.997601: ATLAS_r009s102, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:48:49.336310: predicting ATLAS_r009s103 
2024-03-31 16:48:49.338212: ATLAS_r009s103, shape torch.Size([1, 150, 182, 145]), rank 0 
2024-03-31 16:48:51.672706: predicting ATLAS_r009s105 
2024-03-31 16:48:51.674639: ATLAS_r009s105, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:48:54.010985: predicting ATLAS_r009s106 
2024-03-31 16:48:54.013278: ATLAS_r009s106, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:48:56.431165: predicting ATLAS_r009s107 
2024-03-31 16:48:56.432591: ATLAS_r009s107, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:48:58.767609: predicting ATLAS_r009s108 
2024-03-31 16:48:58.799026: ATLAS_r009s108, shape torch.Size([1, 192, 229, 193]), rank 0 
2024-03-31 16:49:02.320237: predicting ATLAS_r009s109 
2024-03-31 16:49:02.322877: ATLAS_r009s109, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:49:04.738438: predicting ATLAS_r009s110 
2024-03-31 16:49:04.752451: ATLAS_r009s110, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:49:07.156651: predicting ATLAS_r009s111 
2024-03-31 16:49:07.158581: ATLAS_r009s111, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:49:09.508238: predicting ATLAS_r009s113 
2024-03-31 16:49:09.510223: ATLAS_r009s113, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:49:11.864932: predicting ATLAS_r009s114 
2024-03-31 16:49:11.867105: ATLAS_r009s114, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:49:14.224125: predicting ATLAS_r009s115 
2024-03-31 16:49:14.226132: ATLAS_r009s115, shape torch.Size([1, 146, 182, 145]), rank 0 
2024-03-31 16:49:16.561496: predicting ATLAS_r009s117 
2024-03-31 16:49:16.564058: ATLAS_r009s117, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:49:18.903611: predicting ATLAS_r009s118 
2024-03-31 16:49:18.906198: ATLAS_r009s118, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:49:21.338178: predicting ATLAS_r009s119 
2024-03-31 16:49:21.372400: ATLAS_r009s119, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:49:23.830526: predicting ATLAS_r009s120 
2024-03-31 16:49:23.838678: ATLAS_r009s120, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:49:26.174414: predicting ATLAS_r009s121 
2024-03-31 16:49:26.176073: ATLAS_r009s121, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:49:28.546282: predicting ATLAS_r009s122 
2024-03-31 16:49:28.567158: ATLAS_r009s122, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:49:30.949497: predicting ATLAS_r009s123 
2024-03-31 16:49:30.986075: ATLAS_r009s123, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:49:33.411849: predicting ATLAS_r009s124 
2024-03-31 16:49:33.437456: ATLAS_r009s124, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:49:35.817038: predicting ATLAS_r009s125 
2024-03-31 16:49:35.818878: ATLAS_r009s125, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:49:38.241956: predicting ATLAS_r009s126 
2024-03-31 16:49:38.278337: ATLAS_r009s126, shape torch.Size([1, 149, 180, 145]), rank 0 
2024-03-31 16:49:40.720957: predicting ATLAS_r010s001 
2024-03-31 16:49:40.765433: ATLAS_r010s001, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:49:43.162740: predicting ATLAS_r010s002 
2024-03-31 16:49:43.164707: ATLAS_r010s002, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:49:45.498994: predicting ATLAS_r010s003 
2024-03-31 16:49:45.500822: ATLAS_r010s003, shape torch.Size([1, 191, 229, 193]), rank 0 
2024-03-31 16:49:49.191104: predicting ATLAS_r010s004 
2024-03-31 16:49:49.193828: ATLAS_r010s004, shape torch.Size([1, 191, 229, 193]), rank 0 
2024-03-31 16:49:52.717231: predicting ATLAS_r010s005 
2024-03-31 16:49:52.720823: ATLAS_r010s005, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:49:55.062100: predicting ATLAS_r010s006 
2024-03-31 16:49:55.063868: ATLAS_r010s006, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 16:49:57.439718: predicting ATLAS_r010s007 
2024-03-31 16:49:57.441701: ATLAS_r010s007, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:49:59.803854: predicting ATLAS_r010s008 
2024-03-31 16:49:59.805710: ATLAS_r010s008, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:50:02.144489: predicting ATLAS_r010s009 
2024-03-31 16:50:02.146818: ATLAS_r010s009, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 16:50:04.585389: predicting ATLAS_r010s010 
2024-03-31 16:50:04.587707: ATLAS_r010s010, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:50:06.926987: predicting ATLAS_r010s011 
2024-03-31 16:50:06.929103: ATLAS_r010s011, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:50:09.265474: predicting ATLAS_r010s012 
2024-03-31 16:50:09.267349: ATLAS_r010s012, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:50:11.606270: predicting ATLAS_r010s013 
2024-03-31 16:50:11.609722: ATLAS_r010s013, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:50:13.945987: predicting ATLAS_r010s014 
2024-03-31 16:50:13.947684: ATLAS_r010s014, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:50:16.285089: predicting ATLAS_r010s016 
2024-03-31 16:50:16.286999: ATLAS_r010s016, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:50:18.627238: predicting ATLAS_r010s018 
2024-03-31 16:50:18.659871: ATLAS_r010s018, shape torch.Size([1, 193, 229, 193]), rank 0 
2024-03-31 16:50:24.099717: predicting ATLAS_r010s019 
2024-03-31 16:50:24.112741: ATLAS_r010s019, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:50:26.453860: predicting ATLAS_r010s021 
2024-03-31 16:50:26.456350: ATLAS_r010s021, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:50:28.795888: predicting ATLAS_r010s022 
2024-03-31 16:50:28.797896: ATLAS_r010s022, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:50:31.133562: predicting ATLAS_r010s023 
2024-03-31 16:50:31.151001: ATLAS_r010s023, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:50:33.557501: predicting ATLAS_r010s024 
2024-03-31 16:50:33.559248: ATLAS_r010s024, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:50:35.898774: predicting ATLAS_r010s026 
2024-03-31 16:50:35.900530: ATLAS_r010s026, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:50:38.241580: predicting ATLAS_r010s027 
2024-03-31 16:50:38.249829: ATLAS_r010s027, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:50:40.667271: predicting ATLAS_r010s028 
2024-03-31 16:50:40.677794: ATLAS_r010s028, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:50:43.017663: predicting ATLAS_r010s029 
2024-03-31 16:50:43.039663: ATLAS_r010s029, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:50:45.375852: predicting ATLAS_r010s032 
2024-03-31 16:50:45.377574: ATLAS_r010s032, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 16:50:47.808270: predicting ATLAS_r011s001 
2024-03-31 16:50:47.810316: ATLAS_r011s001, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:50:50.150988: predicting ATLAS_r011s002 
2024-03-31 16:50:50.152608: ATLAS_r011s002, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:50:52.492674: predicting ATLAS_r011s003 
2024-03-31 16:50:52.494705: ATLAS_r011s003, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:50:54.942657: predicting ATLAS_r011s008 
2024-03-31 16:50:54.960949: ATLAS_r011s008, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:50:57.299133: predicting ATLAS_r011s010 
2024-03-31 16:50:57.314953: ATLAS_r011s010, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:50:59.838750: predicting ATLAS_r011s011 
2024-03-31 16:50:59.841810: ATLAS_r011s011, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:51:02.245399: predicting ATLAS_r011s012 
2024-03-31 16:51:02.252307: ATLAS_r011s012, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:51:04.656837: predicting ATLAS_r011s013 
2024-03-31 16:51:04.659892: ATLAS_r011s013, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:51:06.999749: predicting ATLAS_r011s014 
2024-03-31 16:51:07.040240: ATLAS_r011s014, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:51:09.472994: predicting ATLAS_r011s015 
2024-03-31 16:51:09.491158: ATLAS_r011s015, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 16:51:11.828652: predicting ATLAS_r011s016 
2024-03-31 16:51:11.830405: ATLAS_r011s016, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:51:14.200567: predicting ATLAS_r011s017 
2024-03-31 16:51:14.202531: ATLAS_r011s017, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:51:16.539466: predicting ATLAS_r011s018 
2024-03-31 16:51:16.541378: ATLAS_r011s018, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:51:18.881741: predicting ATLAS_r011s019 
2024-03-31 16:51:18.934897: ATLAS_r011s019, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 16:51:21.389695: predicting ATLAS_r011s020 
2024-03-31 16:51:21.391518: ATLAS_r011s020, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 16:51:23.730690: predicting ATLAS_r011s021 
2024-03-31 16:51:23.733285: ATLAS_r011s021, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:51:26.073584: predicting ATLAS_r011s022 
2024-03-31 16:51:26.087260: ATLAS_r011s022, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:51:28.471449: predicting ATLAS_r011s023 
2024-03-31 16:51:28.473997: ATLAS_r011s023, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:51:30.847856: predicting ATLAS_r011s024 
2024-03-31 16:51:30.850126: ATLAS_r011s024, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:51:33.192026: predicting ATLAS_r011s025 
2024-03-31 16:51:33.193849: ATLAS_r011s025, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:51:35.534316: predicting ATLAS_r011s026 
2024-03-31 16:51:35.535704: ATLAS_r011s026, shape torch.Size([1, 193, 229, 193]), rank 0 
2024-03-31 16:51:40.791964: predicting ATLAS_r011s027 
2024-03-31 16:51:40.796060: ATLAS_r011s027, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:51:43.139205: predicting ATLAS_r011s028 
2024-03-31 16:51:43.410386: ATLAS_r011s028, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:51:45.806918: predicting ATLAS_r011s029 
2024-03-31 16:51:45.822317: ATLAS_r011s029, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:51:48.313455: predicting ATLAS_r011s030 
2024-03-31 16:51:48.316603: ATLAS_r011s030, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:51:50.656123: predicting ATLAS_r011s031 
2024-03-31 16:51:50.657894: ATLAS_r011s031, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:51:52.993714: predicting ATLAS_r011s032 
2024-03-31 16:51:52.995568: ATLAS_r011s032, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:51:55.352988: predicting ATLAS_r011s033 
2024-03-31 16:51:55.355922: ATLAS_r011s033, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:51:57.693145: predicting ATLAS_r011s034 
2024-03-31 16:51:57.694919: ATLAS_r011s034, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:52:00.034060: predicting ATLAS_r014s002 
2024-03-31 16:52:00.036113: ATLAS_r014s002, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:02.475479: predicting ATLAS_r014s004 
2024-03-31 16:52:02.477293: ATLAS_r014s004, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:52:04.897039: predicting ATLAS_r014s008 
2024-03-31 16:52:04.910944: ATLAS_r014s008, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:52:07.529801: predicting ATLAS_r014s010 
2024-03-31 16:52:07.531836: ATLAS_r014s010, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:09.993327: predicting ATLAS_r014s015 
2024-03-31 16:52:10.363879: ATLAS_r014s015, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:13.585129: predicting ATLAS_r015s001 
2024-03-31 16:52:13.603920: ATLAS_r015s001, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:16.025907: predicting ATLAS_r015s006 
2024-03-31 16:52:16.027701: ATLAS_r015s006, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:18.472486: predicting ATLAS_r015s008 
2024-03-31 16:52:18.485857: ATLAS_r015s008, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:20.819723: predicting ATLAS_r015s009 
2024-03-31 16:52:20.821654: ATLAS_r015s009, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:23.245537: predicting ATLAS_r015s010 
2024-03-31 16:52:23.248636: ATLAS_r015s010, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:25.597496: predicting ATLAS_r015s011 
2024-03-31 16:52:25.599486: ATLAS_r015s011, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:27.933815: predicting ATLAS_r015s016 
2024-03-31 16:52:27.935139: ATLAS_r015s016, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:30.270827: predicting ATLAS_r015s018 
2024-03-31 16:52:30.317638: ATLAS_r015s018, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:32.732405: predicting ATLAS_r015s019 
2024-03-31 16:52:32.734337: ATLAS_r015s019, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:35.131193: predicting ATLAS_r015s025 
2024-03-31 16:52:35.133274: ATLAS_r015s025, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:37.471705: predicting ATLAS_r015s026 
2024-03-31 16:52:37.480719: ATLAS_r015s026, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:39.869057: predicting ATLAS_r015s027 
2024-03-31 16:52:39.888211: ATLAS_r015s027, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:42.223587: predicting ATLAS_r017s104 
2024-03-31 16:52:42.226391: ATLAS_r017s104, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:44.562974: predicting ATLAS_r017s105 
2024-03-31 16:52:44.565097: ATLAS_r017s105, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:46.906114: predicting ATLAS_r017s106 
2024-03-31 16:52:46.922126: ATLAS_r017s106, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:49.348732: predicting ATLAS_r017s110 
2024-03-31 16:52:49.371870: ATLAS_r017s110, shape torch.Size([1, 193, 229, 193]), rank 0 
2024-03-31 16:52:54.674435: predicting ATLAS_r017s116 
2024-03-31 16:52:54.711486: ATLAS_r017s116, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:57.181074: predicting ATLAS_r017s117 
2024-03-31 16:52:57.204613: ATLAS_r017s117, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:52:59.771363: predicting ATLAS_r017s118 
2024-03-31 16:52:59.772958: ATLAS_r017s118, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:53:02.113320: predicting ATLAS_r017s119 
2024-03-31 16:53:02.115302: ATLAS_r017s119, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:53:04.514118: predicting ATLAS_r018s001 
2024-03-31 16:53:04.515991: ATLAS_r018s001, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:53:06.856077: predicting ATLAS_r018s007 
2024-03-31 16:53:06.882962: ATLAS_r018s007, shape torch.Size([1, 191, 229, 193]), rank 0 
2024-03-31 16:53:10.514908: predicting ATLAS_r018s008 
2024-03-31 16:53:10.518568: ATLAS_r018s008, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:53:12.862701: predicting ATLAS_r018s010 
2024-03-31 16:53:12.864292: ATLAS_r018s010, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:53:15.205098: predicting ATLAS_r018s011 
2024-03-31 16:53:15.207405: ATLAS_r018s011, shape torch.Size([1, 150, 182, 145]), rank 0 
2024-03-31 16:53:17.570539: predicting ATLAS_r018s012 
2024-03-31 16:53:17.583858: ATLAS_r018s012, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:53:19.981933: predicting ATLAS_r019s001 
2024-03-31 16:53:19.992901: ATLAS_r019s001, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:53:22.347688: predicting ATLAS_r019s004 
2024-03-31 16:53:22.369601: ATLAS_r019s004, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:53:24.821979: predicting ATLAS_r019s007 
2024-03-31 16:53:24.824039: ATLAS_r019s007, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:53:27.166790: predicting ATLAS_r019s008 
2024-03-31 16:53:27.191258: ATLAS_r019s008, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:53:29.661420: predicting ATLAS_r019s009 
2024-03-31 16:53:29.664277: ATLAS_r019s009, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:53:32.168336: predicting ATLAS_r019s010 
2024-03-31 16:53:32.170523: ATLAS_r019s010, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:53:34.507765: predicting ATLAS_r019s012 
2024-03-31 16:53:34.509631: ATLAS_r019s012, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:53:36.850212: predicting ATLAS_r023s001 
2024-03-31 16:53:36.852958: ATLAS_r023s001, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:53:39.193616: predicting ATLAS_r023s002 
2024-03-31 16:53:39.211819: ATLAS_r023s002, shape torch.Size([1, 147, 182, 145]), rank 0 
2024-03-31 16:53:41.547610: predicting ATLAS_r023s003 
2024-03-31 16:53:41.560903: ATLAS_r023s003, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:53:43.928965: predicting ATLAS_r023s007 
2024-03-31 16:53:43.931360: ATLAS_r023s007, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:53:46.279537: predicting ATLAS_r023s008 
2024-03-31 16:53:46.282403: ATLAS_r023s008, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:53:48.623363: predicting ATLAS_r023s009 
2024-03-31 16:53:48.625688: ATLAS_r023s009, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:53:51.023664: predicting ATLAS_r023s014 
2024-03-31 16:53:51.033675: ATLAS_r023s014, shape torch.Size([1, 147, 182, 145]), rank 0 
2024-03-31 16:53:53.395963: predicting ATLAS_r023s017 
2024-03-31 16:53:53.397981: ATLAS_r023s017, shape torch.Size([1, 188, 229, 193]), rank 0 
2024-03-31 16:53:56.912781: predicting ATLAS_r024s002 
2024-03-31 16:53:56.915499: ATLAS_r024s002, shape torch.Size([1, 191, 229, 193]), rank 0 
2024-03-31 16:54:00.453675: predicting ATLAS_r024s003 
2024-03-31 16:54:00.456924: ATLAS_r024s003, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:54:02.831143: predicting ATLAS_r024s005 
2024-03-31 16:54:02.833034: ATLAS_r024s005, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:54:05.191714: predicting ATLAS_r024s008 
2024-03-31 16:54:05.193668: ATLAS_r024s008, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:54:07.532987: predicting ATLAS_r024s011 
2024-03-31 16:54:07.535840: ATLAS_r024s011, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:54:09.877693: predicting ATLAS_r024s012 
2024-03-31 16:54:09.891878: ATLAS_r024s012, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:54:12.230833: predicting ATLAS_r024s013 
2024-03-31 16:54:12.232430: ATLAS_r024s013, shape torch.Size([1, 192, 229, 193]), rank 0 
2024-03-31 16:54:15.743817: predicting ATLAS_r024s014 
2024-03-31 16:54:15.747139: ATLAS_r024s014, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:54:18.147679: predicting ATLAS_r024s018 
2024-03-31 16:54:18.149966: ATLAS_r024s018, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:54:20.489826: predicting ATLAS_r024s019 
2024-03-31 16:54:20.511635: ATLAS_r024s019, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:54:22.855130: predicting ATLAS_r024s021 
2024-03-31 16:54:22.884776: ATLAS_r024s021, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:54:25.283422: predicting ATLAS_r027s001 
2024-03-31 16:54:25.285089: ATLAS_r027s001, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:54:27.623150: predicting ATLAS_r027s006 
2024-03-31 16:54:27.633380: ATLAS_r027s006, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:54:29.971888: predicting ATLAS_r027s009 
2024-03-31 16:54:29.973850: ATLAS_r027s009, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:54:32.312615: predicting ATLAS_r027s013 
2024-03-31 16:54:32.342500: ATLAS_r027s013, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:54:34.812403: predicting ATLAS_r027s015 
2024-03-31 16:54:34.814500: ATLAS_r027s015, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:54:37.152533: predicting ATLAS_r027s017 
2024-03-31 16:54:37.154324: ATLAS_r027s017, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:54:39.497434: predicting ATLAS_r027s023 
2024-03-31 16:54:39.499303: ATLAS_r027s023, shape torch.Size([1, 191, 229, 193]), rank 0 
2024-03-31 16:54:43.017709: predicting ATLAS_r027s031 
2024-03-31 16:54:43.020283: ATLAS_r027s031, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:54:45.363434: predicting ATLAS_r027s032 
2024-03-31 16:54:45.374676: ATLAS_r027s032, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:54:47.818616: predicting ATLAS_r027s035 
2024-03-31 16:54:47.820394: ATLAS_r027s035, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:54:50.158482: predicting ATLAS_r027s041 
2024-03-31 16:54:50.160189: ATLAS_r027s041, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:54:52.498633: predicting ATLAS_r027s042 
2024-03-31 16:54:52.511819: ATLAS_r027s042, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:54:54.858235: predicting ATLAS_r027s047 
2024-03-31 16:54:54.873625: ATLAS_r027s047, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 16:54:57.290908: predicting ATLAS_r027s048 
2024-03-31 16:54:57.293733: ATLAS_r027s048, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:54:59.635701: predicting ATLAS_r027s050 
2024-03-31 16:54:59.646307: ATLAS_r027s050, shape torch.Size([1, 190, 229, 193]), rank 0 
2024-03-31 16:55:03.280857: predicting ATLAS_r027s052 
2024-03-31 16:55:03.283489: ATLAS_r027s052, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:55:05.623282: predicting ATLAS_r028s002 
2024-03-31 16:55:05.625184: ATLAS_r028s002, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:07.963891: predicting ATLAS_r028s004 
2024-03-31 16:55:07.981868: ATLAS_r028s004, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:10.320854: predicting ATLAS_r028s005 
2024-03-31 16:55:10.356251: ATLAS_r028s005, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:12.788126: predicting ATLAS_r028s007 
2024-03-31 16:55:12.789998: ATLAS_r028s007, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:15.135343: predicting ATLAS_r028s008 
2024-03-31 16:55:15.138509: ATLAS_r028s008, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:17.476450: predicting ATLAS_r028s009 
2024-03-31 16:55:17.504171: ATLAS_r028s009, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:19.908365: predicting ATLAS_r028s011 
2024-03-31 16:55:19.910402: ATLAS_r028s011, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:22.335577: predicting ATLAS_r028s012 
2024-03-31 16:55:22.337270: ATLAS_r028s012, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:24.678223: predicting ATLAS_r028s013 
2024-03-31 16:55:24.721642: ATLAS_r028s013, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:27.179327: predicting ATLAS_r028s014 
2024-03-31 16:55:27.226010: ATLAS_r028s014, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:29.666994: predicting ATLAS_r028s017 
2024-03-31 16:55:29.669229: ATLAS_r028s017, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:32.006170: predicting ATLAS_r028s020 
2024-03-31 16:55:32.007912: ATLAS_r028s020, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:34.370880: predicting ATLAS_r028s026 
2024-03-31 16:55:34.372898: ATLAS_r028s026, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:36.732277: predicting ATLAS_r029s003 
2024-03-31 16:55:36.752625: ATLAS_r029s003, shape torch.Size([1, 190, 229, 193]), rank 0 
2024-03-31 16:55:40.297671: predicting ATLAS_r029s004 
2024-03-31 16:55:40.300231: ATLAS_r029s004, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:42.641264: predicting ATLAS_r029s007 
2024-03-31 16:55:42.663061: ATLAS_r029s007, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:45.074622: predicting ATLAS_r029s009 
2024-03-31 16:55:45.077272: ATLAS_r029s009, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:47.419338: predicting ATLAS_r029s010 
2024-03-31 16:55:47.506077: ATLAS_r029s010, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:49.848424: predicting ATLAS_r031s001 
2024-03-31 16:55:49.868549: ATLAS_r031s001, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:52.241851: predicting ATLAS_r031s002 
2024-03-31 16:55:52.243576: ATLAS_r031s002, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:54.583715: predicting ATLAS_r031s003 
2024-03-31 16:55:54.586795: ATLAS_r031s003, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:56.926805: predicting ATLAS_r031s004 
2024-03-31 16:55:56.940200: ATLAS_r031s004, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:55:59.289405: predicting ATLAS_r031s005 
2024-03-31 16:55:59.298074: ATLAS_r031s005, shape torch.Size([1, 193, 229, 193]), rank 0 
2024-03-31 16:56:04.678064: predicting ATLAS_r031s006 
2024-03-31 16:56:04.686617: ATLAS_r031s006, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:07.067169: predicting ATLAS_r031s007 
2024-03-31 16:56:07.068790: ATLAS_r031s007, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:09.411478: predicting ATLAS_r031s008 
2024-03-31 16:56:09.413328: ATLAS_r031s008, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:11.752301: predicting ATLAS_r031s009 
2024-03-31 16:56:11.797107: ATLAS_r031s009, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:14.139085: predicting ATLAS_r031s010 
2024-03-31 16:56:14.173173: ATLAS_r031s010, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:16.962432: predicting ATLAS_r031s011 
2024-03-31 16:56:16.965061: ATLAS_r031s011, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:19.308744: predicting ATLAS_r031s012 
2024-03-31 16:56:19.310915: ATLAS_r031s012, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:21.709817: predicting ATLAS_r031s013 
2024-03-31 16:56:21.711859: ATLAS_r031s013, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:24.062206: predicting ATLAS_r031s014 
2024-03-31 16:56:24.063947: ATLAS_r031s014, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:26.402487: predicting ATLAS_r031s015 
2024-03-31 16:56:26.403938: ATLAS_r031s015, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:28.744345: predicting ATLAS_r031s016 
2024-03-31 16:56:28.746894: ATLAS_r031s016, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:31.084448: predicting ATLAS_r031s017 
2024-03-31 16:56:31.101867: ATLAS_r031s017, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:33.438764: predicting ATLAS_r031s018 
2024-03-31 16:56:33.458190: ATLAS_r031s018, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:35.923618: predicting ATLAS_r031s019 
2024-03-31 16:56:35.925526: ATLAS_r031s019, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:38.284470: predicting ATLAS_r031s020 
2024-03-31 16:56:38.286374: ATLAS_r031s020, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:40.625652: predicting ATLAS_r031s021 
2024-03-31 16:56:40.628142: ATLAS_r031s021, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:42.964154: predicting ATLAS_r031s022 
2024-03-31 16:56:42.984181: ATLAS_r031s022, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:45.319721: predicting ATLAS_r031s023 
2024-03-31 16:56:45.322060: ATLAS_r031s023, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:47.658675: predicting ATLAS_r031s024 
2024-03-31 16:56:47.687966: ATLAS_r031s024, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:50.162382: predicting ATLAS_r031s025 
2024-03-31 16:56:50.164523: ATLAS_r031s025, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:52.570168: predicting ATLAS_r031s026 
2024-03-31 16:56:52.582117: ATLAS_r031s026, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:55.033152: predicting ATLAS_r031s027 
2024-03-31 16:56:55.048683: ATLAS_r031s027, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:57.472339: predicting ATLAS_r031s028 
2024-03-31 16:56:57.530989: ATLAS_r031s028, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:56:59.911802: predicting ATLAS_r031s029 
2024-03-31 16:56:59.913668: ATLAS_r031s029, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:02.256607: predicting ATLAS_r031s030 
2024-03-31 16:57:02.317996: ATLAS_r031s030, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:04.797419: predicting ATLAS_r031s031 
2024-03-31 16:57:04.816126: ATLAS_r031s031, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:07.150867: predicting ATLAS_r031s032 
2024-03-31 16:57:07.158078: ATLAS_r031s032, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:09.635687: predicting ATLAS_r031s033 
2024-03-31 16:57:09.637436: ATLAS_r031s033, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:12.006896: predicting ATLAS_r031s034 
2024-03-31 16:57:12.008796: ATLAS_r031s034, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:14.343983: predicting ATLAS_r031s035 
2024-03-31 16:57:14.375112: ATLAS_r031s035, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:16.711366: predicting ATLAS_r031s036 
2024-03-31 16:57:16.749120: ATLAS_r031s036, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:19.200697: predicting ATLAS_r031s037 
2024-03-31 16:57:19.202618: ATLAS_r031s037, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:21.537963: predicting ATLAS_r034s003 
2024-03-31 16:57:21.547424: ATLAS_r034s003, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:23.883562: predicting ATLAS_r034s006 
2024-03-31 16:57:23.885621: ATLAS_r034s006, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:26.222338: predicting ATLAS_r034s007 
2024-03-31 16:57:26.224300: ATLAS_r034s007, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:28.590837: predicting ATLAS_r034s008 
2024-03-31 16:57:28.592146: ATLAS_r034s008, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:30.927481: predicting ATLAS_r034s009 
2024-03-31 16:57:30.929405: ATLAS_r034s009, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:57:33.264944: predicting ATLAS_r034s010 
2024-03-31 16:57:33.266828: ATLAS_r034s010, shape torch.Size([1, 189, 229, 193]), rank 0 
2024-03-31 16:57:36.792701: predicting ATLAS_r034s011 
2024-03-31 16:57:36.794933: ATLAS_r034s011, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:57:39.132775: predicting ATLAS_r034s012 
2024-03-31 16:57:39.143692: ATLAS_r034s012, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:41.482347: predicting ATLAS_r034s013 
2024-03-31 16:57:41.506402: ATLAS_r034s013, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:43.924631: predicting ATLAS_r034s014 
2024-03-31 16:57:43.926781: ATLAS_r034s014, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:57:46.262754: predicting ATLAS_r034s015 
2024-03-31 16:57:46.265211: ATLAS_r034s015, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:57:48.601233: predicting ATLAS_r034s021 
2024-03-31 16:57:48.613523: ATLAS_r034s021, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:50.959478: predicting ATLAS_r034s022 
2024-03-31 16:57:50.976179: ATLAS_r034s022, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:57:53.311772: predicting ATLAS_r034s025 
2024-03-31 16:57:53.344323: ATLAS_r034s025, shape torch.Size([1, 150, 182, 145]), rank 0 
2024-03-31 16:57:55.723951: predicting ATLAS_r034s026 
2024-03-31 16:57:55.725758: ATLAS_r034s026, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:57:58.064336: predicting ATLAS_r034s032 
2024-03-31 16:57:58.074730: ATLAS_r034s032, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:58:00.414690: predicting ATLAS_r034s033 
2024-03-31 16:58:00.417071: ATLAS_r034s033, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:58:02.760588: predicting ATLAS_r034s036 
2024-03-31 16:58:02.762421: ATLAS_r034s036, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:58:05.098807: predicting ATLAS_r034s037 
2024-03-31 16:58:05.100740: ATLAS_r034s037, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:58:07.510858: predicting ATLAS_r034s039 
2024-03-31 16:58:07.525591: ATLAS_r034s039, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:58:09.860978: predicting ATLAS_r034s040 
2024-03-31 16:58:09.870164: ATLAS_r034s040, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:58:12.303834: predicting ATLAS_r034s046 
2024-03-31 16:58:12.316194: ATLAS_r034s046, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:58:14.653555: predicting ATLAS_r034s047 
2024-03-31 16:58:14.655556: ATLAS_r034s047, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:58:16.991532: predicting ATLAS_r034s048 
2024-03-31 16:58:17.021444: ATLAS_r034s048, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:58:19.570001: predicting ATLAS_r035s003 
2024-03-31 16:58:19.571725: ATLAS_r035s003, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:58:22.085901: predicting ATLAS_r035s005 
2024-03-31 16:58:22.111938: ATLAS_r035s005, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:58:24.539029: predicting ATLAS_r035s006 
2024-03-31 16:58:24.542238: ATLAS_r035s006, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:58:26.881585: predicting ATLAS_r035s007 
2024-03-31 16:58:26.910880: ATLAS_r035s007, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:58:29.579824: predicting ATLAS_r035s011 
2024-03-31 16:58:29.581652: ATLAS_r035s011, shape torch.Size([1, 191, 229, 193]), rank 0 
2024-03-31 16:58:33.235975: predicting ATLAS_r035s012 
2024-03-31 16:58:33.238592: ATLAS_r035s012, shape torch.Size([1, 193, 229, 193]), rank 0 
2024-03-31 16:58:38.494939: predicting ATLAS_r035s013 
2024-03-31 16:58:38.497646: ATLAS_r035s013, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:58:40.839554: predicting ATLAS_r035s014 
2024-03-31 16:58:40.909542: ATLAS_r035s014, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:58:43.320779: predicting ATLAS_r038s005 
2024-03-31 16:58:43.322974: ATLAS_r038s005, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:58:45.662175: predicting ATLAS_r038s006 
2024-03-31 16:58:45.726055: ATLAS_r038s006, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:58:48.207785: predicting ATLAS_r038s007 
2024-03-31 16:58:48.210871: ATLAS_r038s007, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 16:58:50.675380: predicting ATLAS_r038s010 
2024-03-31 16:58:50.684538: ATLAS_r038s010, shape torch.Size([1, 190, 229, 193]), rank 0 
2024-03-31 16:58:54.330808: predicting ATLAS_r038s014 
2024-03-31 16:58:54.333470: ATLAS_r038s014, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 16:58:56.674690: predicting ATLAS_r038s016 
2024-03-31 16:58:56.676566: ATLAS_r038s016, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:58:59.016243: predicting ATLAS_r038s017 
2024-03-31 16:58:59.019154: ATLAS_r038s017, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:59:01.354842: predicting ATLAS_r038s018 
2024-03-31 16:59:01.356785: ATLAS_r038s018, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:59:03.692594: predicting ATLAS_r038s020 
2024-03-31 16:59:03.724847: ATLAS_r038s020, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 16:59:06.062265: predicting ATLAS_r038s021 
2024-03-31 16:59:06.068083: ATLAS_r038s021, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:59:08.407815: predicting ATLAS_r038s022 
2024-03-31 16:59:08.411288: ATLAS_r038s022, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:59:10.807882: predicting ATLAS_r038s023 
2024-03-31 16:59:10.823991: ATLAS_r038s023, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:59:13.160094: predicting ATLAS_r038s024 
2024-03-31 16:59:13.182859: ATLAS_r038s024, shape torch.Size([1, 148, 182, 145]), rank 0 
2024-03-31 16:59:15.522024: predicting ATLAS_r038s025 
2024-03-31 16:59:15.524405: ATLAS_r038s025, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:59:17.863260: predicting ATLAS_r038s026 
2024-03-31 16:59:17.923920: ATLAS_r038s026, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:59:20.391740: predicting ATLAS_r038s028 
2024-03-31 16:59:20.394286: ATLAS_r038s028, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:59:22.732140: predicting ATLAS_r038s032 
2024-03-31 16:59:22.734539: ATLAS_r038s032, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:59:25.069141: predicting ATLAS_r038s033 
2024-03-31 16:59:25.088451: ATLAS_r038s033, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:59:27.529653: predicting ATLAS_r038s035 
2024-03-31 16:59:27.542665: ATLAS_r038s035, shape torch.Size([1, 147, 182, 145]), rank 0 
2024-03-31 16:59:29.903816: predicting ATLAS_r038s036 
2024-03-31 16:59:29.905606: ATLAS_r038s036, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:59:32.241548: predicting ATLAS_r038s040 
2024-03-31 16:59:32.259624: ATLAS_r038s040, shape torch.Size([1, 189, 229, 193]), rank 0 
2024-03-31 16:59:35.774845: predicting ATLAS_r038s041 
2024-03-31 16:59:35.792865: ATLAS_r038s041, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 16:59:38.134780: predicting ATLAS_r038s043 
2024-03-31 16:59:38.137397: ATLAS_r038s043, shape torch.Size([1, 183, 229, 193]), rank 0 
2024-03-31 16:59:41.650980: predicting ATLAS_r038s047 
2024-03-31 16:59:41.668597: ATLAS_r038s047, shape torch.Size([1, 150, 182, 145]), rank 0 
2024-03-31 16:59:44.006375: predicting ATLAS_r038s048 
2024-03-31 16:59:44.007743: ATLAS_r038s048, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:59:46.344759: predicting ATLAS_r038s049 
2024-03-31 16:59:46.346688: ATLAS_r038s049, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 16:59:48.688986: predicting ATLAS_r038s054 
2024-03-31 16:59:48.690755: ATLAS_r038s054, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 16:59:51.029797: predicting ATLAS_r038s056 
2024-03-31 16:59:51.031673: ATLAS_r038s056, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 16:59:53.387594: predicting ATLAS_r038s057 
2024-03-31 16:59:53.389479: ATLAS_r038s057, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 16:59:55.727545: predicting ATLAS_r038s058 
2024-03-31 16:59:55.763075: ATLAS_r038s058, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 16:59:58.204290: predicting ATLAS_r038s060 
2024-03-31 16:59:58.213791: ATLAS_r038s060, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 17:00:00.609492: predicting ATLAS_r038s061 
2024-03-31 17:00:00.611580: ATLAS_r038s061, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:00:02.949803: predicting ATLAS_r038s064 
2024-03-31 17:00:02.951603: ATLAS_r038s064, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:00:05.288182: predicting ATLAS_r038s065 
2024-03-31 17:00:05.290045: ATLAS_r038s065, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 17:00:07.663841: predicting ATLAS_r038s067 
2024-03-31 17:00:07.665468: ATLAS_r038s067, shape torch.Size([1, 188, 229, 193]), rank 0 
2024-03-31 17:00:11.205705: predicting ATLAS_r038s068 
2024-03-31 17:00:11.214803: ATLAS_r038s068, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 17:00:13.698071: predicting ATLAS_r038s069 
2024-03-31 17:00:13.710672: ATLAS_r038s069, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:00:16.117362: predicting ATLAS_r038s071 
2024-03-31 17:00:16.134090: ATLAS_r038s071, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:00:18.564733: predicting ATLAS_r038s074 
2024-03-31 17:00:18.578479: ATLAS_r038s074, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:00:21.018968: predicting ATLAS_r038s078 
2024-03-31 17:00:21.020676: ATLAS_r038s078, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 17:00:23.432885: predicting ATLAS_r038s081 
2024-03-31 17:00:23.447610: ATLAS_r038s081, shape torch.Size([1, 152, 182, 145]), rank 0 
2024-03-31 17:00:25.820430: predicting ATLAS_r038s082 
2024-03-31 17:00:25.822009: ATLAS_r038s082, shape torch.Size([1, 150, 182, 145]), rank 0 
2024-03-31 17:00:28.210662: predicting ATLAS_r038s084 
2024-03-31 17:00:28.212795: ATLAS_r038s084, shape torch.Size([1, 189, 229, 193]), rank 0 
2024-03-31 17:00:31.772794: predicting ATLAS_r038s085 
2024-03-31 17:00:31.792359: ATLAS_r038s085, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 17:00:34.226676: predicting ATLAS_r038s089 
2024-03-31 17:00:34.228991: ATLAS_r038s089, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:00:36.572878: predicting ATLAS_r038s091 
2024-03-31 17:00:36.574857: ATLAS_r038s091, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:00:38.916472: predicting ATLAS_r038s093 
2024-03-31 17:00:38.918927: ATLAS_r038s093, shape torch.Size([1, 189, 229, 191]), rank 0 
2024-03-31 17:00:42.442577: predicting ATLAS_r038s096 
2024-03-31 17:00:42.444889: ATLAS_r038s096, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 17:00:44.785068: predicting ATLAS_r038s097 
2024-03-31 17:00:44.802754: ATLAS_r038s097, shape torch.Size([1, 149, 182, 145]), rank 0 
2024-03-31 17:00:47.243261: predicting ATLAS_r039s002 
2024-03-31 17:00:47.245260: ATLAS_r039s002, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:00:49.586988: predicting ATLAS_r039s003 
2024-03-31 17:00:49.588675: ATLAS_r039s003, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:00:51.988790: predicting ATLAS_r040s001 
2024-03-31 17:00:52.008172: ATLAS_r040s001, shape torch.Size([1, 192, 229, 193]), rank 0 
2024-03-31 17:00:55.621324: predicting ATLAS_r040s002 
2024-03-31 17:00:55.623707: ATLAS_r040s002, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:00:57.968266: predicting ATLAS_r040s004 
2024-03-31 17:00:57.969836: ATLAS_r040s004, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:00.311120: predicting ATLAS_r040s008 
2024-03-31 17:01:00.328070: ATLAS_r040s008, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:01:02.678060: predicting ATLAS_r040s010 
2024-03-31 17:01:02.761332: ATLAS_r040s010, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:05.209998: predicting ATLAS_r040s011 
2024-03-31 17:01:05.224764: ATLAS_r040s011, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:07.566646: predicting ATLAS_r040s013 
2024-03-31 17:01:07.576703: ATLAS_r040s013, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:09.917579: predicting ATLAS_r040s015 
2024-03-31 17:01:09.920464: ATLAS_r040s015, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:12.259840: predicting ATLAS_r040s016 
2024-03-31 17:01:12.262111: ATLAS_r040s016, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:14.731473: predicting ATLAS_r040s017 
2024-03-31 17:01:14.733349: ATLAS_r040s017, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:17.071040: predicting ATLAS_r040s020 
2024-03-31 17:01:17.072959: ATLAS_r040s020, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:19.495770: predicting ATLAS_r040s022 
2024-03-31 17:01:19.498847: ATLAS_r040s022, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:21.838384: predicting ATLAS_r040s024 
2024-03-31 17:01:21.840343: ATLAS_r040s024, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:24.179229: predicting ATLAS_r040s028 
2024-03-31 17:01:24.194409: ATLAS_r040s028, shape torch.Size([1, 191, 229, 193]), rank 0 
2024-03-31 17:01:27.788378: predicting ATLAS_r040s030 
2024-03-31 17:01:27.801888: ATLAS_r040s030, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:30.145427: predicting ATLAS_r040s031 
2024-03-31 17:01:30.160625: ATLAS_r040s031, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:32.529484: predicting ATLAS_r040s032 
2024-03-31 17:01:32.530798: ATLAS_r040s032, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:34.867930: predicting ATLAS_r040s037 
2024-03-31 17:01:34.915422: ATLAS_r040s037, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:37.345659: predicting ATLAS_r040s039 
2024-03-31 17:01:37.347984: ATLAS_r040s039, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:39.729237: predicting ATLAS_r040s042 
2024-03-31 17:01:39.764484: ATLAS_r040s042, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:42.231275: predicting ATLAS_r040s043 
2024-03-31 17:01:42.233119: ATLAS_r040s043, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:44.767694: predicting ATLAS_r040s044 
2024-03-31 17:01:44.769600: ATLAS_r040s044, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:47.109052: predicting ATLAS_r040s045 
2024-03-31 17:01:47.111076: ATLAS_r040s045, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:01:49.447547: predicting ATLAS_r040s046 
2024-03-31 17:01:49.450198: ATLAS_r040s046, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:51.839046: predicting ATLAS_r040s047 
2024-03-31 17:01:51.840952: ATLAS_r040s047, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:54.206291: predicting ATLAS_r040s048 
2024-03-31 17:01:54.208653: ATLAS_r040s048, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:56.584657: predicting ATLAS_r040s049 
2024-03-31 17:01:56.602465: ATLAS_r040s049, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:01:58.982292: predicting ATLAS_r040s051 
2024-03-31 17:01:58.994168: ATLAS_r040s051, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:01.333288: predicting ATLAS_r040s053 
2024-03-31 17:02:01.335034: ATLAS_r040s053, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:03.672414: predicting ATLAS_r040s054 
2024-03-31 17:02:03.674115: ATLAS_r040s054, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:06.011298: predicting ATLAS_r040s056 
2024-03-31 17:02:06.013165: ATLAS_r040s056, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:08.350956: predicting ATLAS_r040s059 
2024-03-31 17:02:08.352701: ATLAS_r040s059, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:10.690066: predicting ATLAS_r040s063 
2024-03-31 17:02:10.736395: ATLAS_r040s063, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:13.194800: predicting ATLAS_r040s064 
2024-03-31 17:02:13.209418: ATLAS_r040s064, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:15.641133: predicting ATLAS_r040s067 
2024-03-31 17:02:15.673801: ATLAS_r040s067, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:18.118988: predicting ATLAS_r040s069 
2024-03-31 17:02:18.171849: ATLAS_r040s069, shape torch.Size([1, 191, 229, 193]), rank 0 
2024-03-31 17:02:21.992948: predicting ATLAS_r040s070 
2024-03-31 17:02:21.996947: ATLAS_r040s070, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:24.339945: predicting ATLAS_r040s071 
2024-03-31 17:02:24.341909: ATLAS_r040s071, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:26.680255: predicting ATLAS_r040s072 
2024-03-31 17:02:26.695608: ATLAS_r040s072, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:29.097398: predicting ATLAS_r040s074 
2024-03-31 17:02:29.110988: ATLAS_r040s074, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:31.454203: predicting ATLAS_r040s075 
2024-03-31 17:02:31.460980: ATLAS_r040s075, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:33.830452: predicting ATLAS_r040s076 
2024-03-31 17:02:33.842797: ATLAS_r040s076, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:36.229776: predicting ATLAS_r040s078 
2024-03-31 17:02:36.231814: ATLAS_r040s078, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:38.572482: predicting ATLAS_r040s085 
2024-03-31 17:02:38.586377: ATLAS_r040s085, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:40.927012: predicting ATLAS_r040s086 
2024-03-31 17:02:40.929046: ATLAS_r040s086, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:43.266200: predicting ATLAS_r042s001 
2024-03-31 17:02:43.267808: ATLAS_r042s001, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:45.604049: predicting ATLAS_r042s003 
2024-03-31 17:02:45.606062: ATLAS_r042s003, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:47.946811: predicting ATLAS_r042s004 
2024-03-31 17:02:47.948758: ATLAS_r042s004, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:50.387532: predicting ATLAS_r042s008 
2024-03-31 17:02:50.389569: ATLAS_r042s008, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:52.724167: predicting ATLAS_r042s010 
2024-03-31 17:02:52.737521: ATLAS_r042s010, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:02:55.097222: predicting ATLAS_r042s011 
2024-03-31 17:02:55.130283: ATLAS_r042s011, shape torch.Size([1, 193, 229, 193]), rank 0 
2024-03-31 17:03:00.455629: predicting ATLAS_r042s013 
2024-03-31 17:03:00.458812: ATLAS_r042s013, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:03.027989: predicting ATLAS_r042s015 
2024-03-31 17:03:03.044042: ATLAS_r042s015, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:03:05.492266: predicting ATLAS_r042s018 
2024-03-31 17:03:05.494418: ATLAS_r042s018, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:07.973346: predicting ATLAS_r042s021 
2024-03-31 17:03:07.975384: ATLAS_r042s021, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:03:10.379701: predicting ATLAS_r042s023 
2024-03-31 17:03:10.381682: ATLAS_r042s023, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:12.774179: predicting ATLAS_r042s025 
2024-03-31 17:03:12.787800: ATLAS_r042s025, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:15.189258: predicting ATLAS_r042s028 
2024-03-31 17:03:15.197375: ATLAS_r042s028, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:17.537165: predicting ATLAS_r042s029 
2024-03-31 17:03:17.540280: ATLAS_r042s029, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:19.879692: predicting ATLAS_r042s030 
2024-03-31 17:03:19.881451: ATLAS_r042s030, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:22.216880: predicting ATLAS_r042s032 
2024-03-31 17:03:22.267476: ATLAS_r042s032, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 17:03:24.695052: predicting ATLAS_r042s033 
2024-03-31 17:03:24.736227: ATLAS_r042s033, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:27.071898: predicting ATLAS_r042s035 
2024-03-31 17:03:27.073847: ATLAS_r042s035, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:29.442013: predicting ATLAS_r044s002 
2024-03-31 17:03:29.462230: ATLAS_r044s002, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:31.802818: predicting ATLAS_r044s003 
2024-03-31 17:03:31.822399: ATLAS_r044s003, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:34.199662: predicting ATLAS_r045s002 
2024-03-31 17:03:34.218677: ATLAS_r045s002, shape torch.Size([1, 191, 229, 193]), rank 0 
2024-03-31 17:03:37.803307: predicting ATLAS_r045s003 
2024-03-31 17:03:37.822346: ATLAS_r045s003, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:40.270789: predicting ATLAS_r046s001 
2024-03-31 17:03:40.282095: ATLAS_r046s001, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:42.729803: predicting ATLAS_r046s005 
2024-03-31 17:03:42.731561: ATLAS_r046s005, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:45.066583: predicting ATLAS_r046s006 
2024-03-31 17:03:45.090685: ATLAS_r046s006, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:47.468215: predicting ATLAS_r046s007 
2024-03-31 17:03:47.485279: ATLAS_r046s007, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:49.910135: predicting ATLAS_r046s008 
2024-03-31 17:03:49.911845: ATLAS_r046s008, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:03:52.252113: predicting ATLAS_r046s011 
2024-03-31 17:03:52.254095: ATLAS_r046s011, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:54.594730: predicting ATLAS_r046s012 
2024-03-31 17:03:54.618838: ATLAS_r046s012, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:57.025655: predicting ATLAS_r047s001 
2024-03-31 17:03:57.047111: ATLAS_r047s001, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:03:59.430427: predicting ATLAS_r047s006 
2024-03-31 17:03:59.432217: ATLAS_r047s006, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:01.768340: predicting ATLAS_r047s007 
2024-03-31 17:04:01.783980: ATLAS_r047s007, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:04.130806: predicting ATLAS_r047s010 
2024-03-31 17:04:04.133027: ATLAS_r047s010, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:06.477383: predicting ATLAS_r047s013 
2024-03-31 17:04:06.526681: ATLAS_r047s013, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:08.991349: predicting ATLAS_r047s014 
2024-03-31 17:04:08.994001: ATLAS_r047s014, shape torch.Size([1, 193, 229, 193]), rank 0 
2024-03-31 17:04:14.258752: predicting ATLAS_r047s015 
2024-03-31 17:04:14.261101: ATLAS_r047s015, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:16.599855: predicting ATLAS_r047s016 
2024-03-31 17:04:16.615995: ATLAS_r047s016, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:18.956762: predicting ATLAS_r047s017 
2024-03-31 17:04:18.958775: ATLAS_r047s017, shape torch.Size([1, 151, 182, 145]), rank 0 
2024-03-31 17:04:21.296523: predicting ATLAS_r047s018 
2024-03-31 17:04:21.299069: ATLAS_r047s018, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:23.638117: predicting ATLAS_r047s021 
2024-03-31 17:04:23.649988: ATLAS_r047s021, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:26.119680: predicting ATLAS_r047s026 
2024-03-31 17:04:26.121571: ATLAS_r047s026, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:28.458080: predicting ATLAS_r047s027 
2024-03-31 17:04:28.460100: ATLAS_r047s027, shape torch.Size([1, 150, 182, 145]), rank 0 
2024-03-31 17:04:30.796839: predicting ATLAS_r047s031 
2024-03-31 17:04:30.879594: ATLAS_r047s031, shape torch.Size([1, 191, 229, 193]), rank 0 
2024-03-31 17:04:34.610935: predicting ATLAS_r047s035 
2024-03-31 17:04:34.639957: ATLAS_r047s035, shape torch.Size([1, 148, 182, 145]), rank 0 
2024-03-31 17:04:36.981093: predicting ATLAS_r047s036 
2024-03-31 17:04:36.993327: ATLAS_r047s036, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:39.403278: predicting ATLAS_r047s037 
2024-03-31 17:04:39.405089: ATLAS_r047s037, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:41.774517: predicting ATLAS_r047s038 
2024-03-31 17:04:41.819777: ATLAS_r047s038, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:44.157198: predicting ATLAS_r047s039 
2024-03-31 17:04:44.173294: ATLAS_r047s039, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:46.566522: predicting ATLAS_r047s041 
2024-03-31 17:04:46.568483: ATLAS_r047s041, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 17:04:48.923728: predicting ATLAS_r047s043 
2024-03-31 17:04:48.925527: ATLAS_r047s043, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:51.268677: predicting ATLAS_r047s044 
2024-03-31 17:04:51.270529: ATLAS_r047s044, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:53.606369: predicting ATLAS_r047s046 
2024-03-31 17:04:53.619762: ATLAS_r047s046, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:56.010626: predicting ATLAS_r047s048 
2024-03-31 17:04:56.058690: ATLAS_r047s048, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:04:58.483770: predicting ATLAS_r047s050 
2024-03-31 17:04:58.512529: ATLAS_r047s050, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:00.911229: predicting ATLAS_r048s002 
2024-03-31 17:05:00.931290: ATLAS_r048s002, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:03.269503: predicting ATLAS_r048s004 
2024-03-31 17:05:03.271026: ATLAS_r048s004, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:05.609413: predicting ATLAS_r048s006 
2024-03-31 17:05:05.612244: ATLAS_r048s006, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:07.971403: predicting ATLAS_r048s008 
2024-03-31 17:05:07.998797: ATLAS_r048s008, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:10.419467: predicting ATLAS_r048s010 
2024-03-31 17:05:10.439543: ATLAS_r048s010, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:12.842815: predicting ATLAS_r048s011 
2024-03-31 17:05:12.860112: ATLAS_r048s011, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:15.276097: predicting ATLAS_r048s012 
2024-03-31 17:05:15.305887: ATLAS_r048s012, shape torch.Size([1, 149, 182, 145]), rank 0 
2024-03-31 17:05:17.756871: predicting ATLAS_r048s014 
2024-03-31 17:05:17.768648: ATLAS_r048s014, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:20.226707: predicting ATLAS_r048s015 
2024-03-31 17:05:20.228521: ATLAS_r048s015, shape torch.Size([1, 189, 229, 193]), rank 0 
2024-03-31 17:05:23.743598: predicting ATLAS_r048s016 
2024-03-31 17:05:23.767003: ATLAS_r048s016, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:05:26.103976: predicting ATLAS_r048s018 
2024-03-31 17:05:26.128170: ATLAS_r048s018, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:28.509348: predicting ATLAS_r048s021 
2024-03-31 17:05:28.512705: ATLAS_r048s021, shape torch.Size([1, 192, 229, 193]), rank 0 
2024-03-31 17:05:32.032070: predicting ATLAS_r048s022 
2024-03-31 17:05:32.034868: ATLAS_r048s022, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:34.374662: predicting ATLAS_r048s029 
2024-03-31 17:05:34.387105: ATLAS_r048s029, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:36.802937: predicting ATLAS_r048s031 
2024-03-31 17:05:36.811656: ATLAS_r048s031, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:39.150752: predicting ATLAS_r048s032 
2024-03-31 17:05:39.191830: ATLAS_r048s032, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:41.532784: predicting ATLAS_r048s034 
2024-03-31 17:05:41.535053: ATLAS_r048s034, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:43.871764: predicting ATLAS_r048s035 
2024-03-31 17:05:43.873749: ATLAS_r048s035, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:46.211692: predicting ATLAS_r048s036 
2024-03-31 17:05:46.213381: ATLAS_r048s036, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:05:48.550471: predicting ATLAS_r048s037 
2024-03-31 17:05:48.552237: ATLAS_r048s037, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:50.946644: predicting ATLAS_r048s038 
2024-03-31 17:05:51.031769: ATLAS_r048s038, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:05:53.433314: predicting ATLAS_r048s039 
2024-03-31 17:05:53.450145: ATLAS_r048s039, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:55.813918: predicting ATLAS_r048s043 
2024-03-31 17:05:55.834556: ATLAS_r048s043, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:05:58.435830: predicting ATLAS_r049s005 
2024-03-31 17:05:58.443561: ATLAS_r049s005, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:06:00.778841: predicting ATLAS_r049s007 
2024-03-31 17:06:00.787009: ATLAS_r049s007, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:06:03.123803: predicting ATLAS_r049s010 
2024-03-31 17:06:03.125542: ATLAS_r049s010, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:06:05.505268: predicting ATLAS_r049s011 
2024-03-31 17:06:05.506993: ATLAS_r049s011, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:06:07.842338: predicting ATLAS_r049s012 
2024-03-31 17:06:07.881523: ATLAS_r049s012, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:06:10.253400: predicting ATLAS_r049s016 
2024-03-31 17:06:10.267444: ATLAS_r049s016, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 17:06:12.674816: predicting ATLAS_r049s018 
2024-03-31 17:06:12.693054: ATLAS_r049s018, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:06:15.082661: predicting ATLAS_r049s020 
2024-03-31 17:06:15.084402: ATLAS_r049s020, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:06:17.423307: predicting ATLAS_r049s024 
2024-03-31 17:06:17.439272: ATLAS_r049s024, shape torch.Size([1, 190, 229, 193]), rank 0 
2024-03-31 17:06:20.985503: predicting ATLAS_r049s025 
2024-03-31 17:06:20.988785: ATLAS_r049s025, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:06:23.330163: predicting ATLAS_r049s026 
2024-03-31 17:06:23.331510: ATLAS_r049s026, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:06:25.667551: predicting ATLAS_r049s028 
2024-03-31 17:06:25.684443: ATLAS_r049s028, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:06:28.025098: predicting ATLAS_r050s002 
2024-03-31 17:06:28.028381: ATLAS_r050s002, shape torch.Size([1, 190, 229, 193]), rank 0 
2024-03-31 17:06:31.630474: predicting ATLAS_r050s003 
2024-03-31 17:06:31.633600: ATLAS_r050s003, shape torch.Size([1, 153, 182, 145]), rank 0 
2024-03-31 17:06:33.978166: predicting ATLAS_r050s005 
2024-03-31 17:06:33.980036: ATLAS_r050s005, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:06:36.327348: predicting ATLAS_r050s006 
2024-03-31 17:06:36.365101: ATLAS_r050s006, shape torch.Size([1, 150, 182, 145]), rank 0 
2024-03-31 17:06:38.752919: predicting ATLAS_r050s008 
2024-03-31 17:06:38.756019: ATLAS_r050s008, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:06:41.095199: predicting ATLAS_r050s009 
2024-03-31 17:06:41.096877: ATLAS_r050s009, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:06:43.498592: predicting ATLAS_r050s015 
2024-03-31 17:06:43.500347: ATLAS_r050s015, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:06:45.919698: predicting ATLAS_r052s001 
2024-03-31 17:06:45.992378: ATLAS_r052s001, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:06:48.348732: predicting ATLAS_r052s002 
2024-03-31 17:06:48.350616: ATLAS_r052s002, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:06:50.710593: predicting ATLAS_r052s007 
2024-03-31 17:06:50.713441: ATLAS_r052s007, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:06:53.117101: predicting ATLAS_r052s010 
2024-03-31 17:06:53.131514: ATLAS_r052s010, shape torch.Size([1, 193, 229, 193]), rank 0 
2024-03-31 17:06:58.386830: predicting ATLAS_r052s011 
2024-03-31 17:06:58.408510: ATLAS_r052s011, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:07:00.752122: predicting ATLAS_r052s014 
2024-03-31 17:07:00.753842: ATLAS_r052s014, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:07:03.091299: predicting ATLAS_r052s015 
2024-03-31 17:07:03.093434: ATLAS_r052s015, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:07:05.478845: predicting ATLAS_r052s016 
2024-03-31 17:07:05.523277: ATLAS_r052s016, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:07:07.908461: predicting ATLAS_r052s019 
2024-03-31 17:07:07.911197: ATLAS_r052s019, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:07:10.249175: predicting ATLAS_r052s021 
2024-03-31 17:07:10.251704: ATLAS_r052s021, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:07:12.609479: predicting ATLAS_r052s023 
2024-03-31 17:07:12.645019: ATLAS_r052s023, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:07:15.022485: predicting ATLAS_r052s025 
2024-03-31 17:07:15.045407: ATLAS_r052s025, shape torch.Size([1, 191, 229, 193]), rank 0 
2024-03-31 17:07:18.912304: predicting ATLAS_r052s026 
2024-03-31 17:07:18.914855: ATLAS_r052s026, shape torch.Size([1, 154, 182, 145]), rank 0 
2024-03-31 17:07:21.258301: predicting ATLAS_r052s027 
2024-03-31 17:07:21.260048: ATLAS_r052s027, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:07:23.600438: predicting ATLAS_r052s029 
2024-03-31 17:07:23.628239: ATLAS_r052s029, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:07:26.021016: predicting ATLAS_r052s031 
2024-03-31 17:07:26.071228: ATLAS_r052s031, shape torch.Size([1, 193, 229, 193]), rank 0 
2024-03-31 17:07:31.654642: predicting ATLAS_r052s032 
2024-03-31 17:07:31.657578: ATLAS_r052s032, shape torch.Size([1, 155, 182, 145]), rank 0 
2024-03-31 17:08:13.873871: Validation complete 
2024-03-31 17:08:13.873956: Mean Validation Dice:  0.7874737735706356 
