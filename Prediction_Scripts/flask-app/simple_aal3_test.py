"""
Simple test for AAL3 integration - basic functionality check.
"""

import os
from brain_region_mapping import AAL_REGIONS, REGION_GROUPS

def test_basic_functionality():
    """Test basic functionality without heavy dependencies."""
    
    print("AAL3 Integration - Basic Functionality Test")
    print("=" * 50)
    
    # Test 1: Check AAL_REGIONS dictionary
    print(f"1. AAL_REGIONS dictionary:")
    print(f"   - Total regions defined: {len(AAL_REGIONS)}")
    print(f"   - Region ID range: {min(AAL_REGIONS.keys())} - {max(AAL_REGIONS.keys())}")
    
    # Show some key regions
    key_regions = [1, 2, 15, 16, 63, 64, 65, 66]  # Motor and parietal areas
    print(f"   - Key regions:")
    for region_id in key_regions:
        if region_id in AAL_REGIONS:
            print(f"     {region_id}: {AAL_REGIONS[region_id]}")
    
    # Test 2: Check REGION_GROUPS
    print(f"\n2. REGION_GROUPS dictionary:")
    print(f"   - Total groups defined: {len(REGION_GROUPS)}")
    
    # Check key groups
    key_groups = ["Motor Areas", "Supplementary Motor Area", "Parietal Lobe"]
    for group in key_groups:
        if group in REGION_GROUPS:
            regions = REGION_GROUPS[group]
            print(f"   - {group}: {len(regions)} regions")
            region_names = [AAL_REGIONS.get(r, f"Unknown_{r}") for r in regions[:3]]
            print(f"     Examples: {', '.join(region_names)}...")
    
    # Test 3: Check AAL file existence
    print(f"\n3. AAL3 Atlas File:")
    script_dir = os.path.dirname(os.path.abspath(__file__))
    aal_atlas_path = os.path.join(script_dir, 'AAL3v1.nii.gz')
    aal_txt_path = os.path.join(script_dir, 'AAL3v1.nii.txt')
    
    if os.path.exists(aal_atlas_path):
        file_size = os.path.getsize(aal_atlas_path) / (1024 * 1024)  # MB
        print(f"   ✅ AAL3v1.nii.gz found ({file_size:.1f} MB)")
    else:
        print(f"   ❌ AAL3v1.nii.gz not found at {aal_atlas_path}")
    
    if os.path.exists(aal_txt_path):
        print(f"   ✅ AAL3v1.nii.txt found")
    else:
        print(f"   ❌ AAL3v1.nii.txt not found")
    
    # Test 4: Verify region consistency
    print(f"\n4. Region Consistency Check:")
    
    # Check if all regions in groups exist in AAL_REGIONS
    missing_regions = []
    for group_name, region_ids in REGION_GROUPS.items():
        for region_id in region_ids:
            if region_id not in AAL_REGIONS:
                missing_regions.append((group_name, region_id))
    
    if missing_regions:
        print(f"   ❌ Found {len(missing_regions)} missing region definitions:")
        for group, region_id in missing_regions[:5]:  # Show first 5
            print(f"     Group '{group}' references undefined region {region_id}")
    else:
        print(f"   ✅ All regions in groups have definitions")
    
    # Test 5: Check specific clinical areas
    print(f"\n5. Clinical Areas Check:")
    
    clinical_checks = {
        "Left Supplementary Motor Area": 15,
        "Right Supplementary Motor Area": 16,
        "Left Superior Parietal": 63,
        "Right Superior Parietal": 64,
        "Left Inferior Parietal": 65,
        "Right Inferior Parietal": 66,
        "Left SupraMarginal": 67,
        "Right SupraMarginal": 68,
        "Left Angular": 69,
        "Right Angular": 70,
    }
    
    for area_name, region_id in clinical_checks.items():
        if region_id in AAL_REGIONS:
            actual_name = AAL_REGIONS[region_id]
            print(f"   ✅ {area_name}: {actual_name}")
        else:
            print(f"   ❌ {area_name}: Region {region_id} not found")
    
    print(f"\n" + "=" * 50)
    print("SUMMARY:")
    print(f"- AAL3 atlas contains {len(AAL_REGIONS)} brain regions")
    print(f"- {len(REGION_GROUPS)} anatomical groups defined")
    print(f"- Enhanced parietal lobe analysis with 4 subdivisions")
    print(f"- Dedicated supplementary motor area tracking")
    print(f"- Atlas file: {'✅ Available' if os.path.exists(aal_atlas_path) else '❌ Missing'}")
    
    if os.path.exists(aal_atlas_path) and len(missing_regions) == 0:
        print(f"\n🎉 AAL3 integration is ready for use!")
        return True
    else:
        print(f"\n⚠️  Some issues found - check the details above")
        return False

if __name__ == "__main__":
    success = test_basic_functionality()
    
    if success:
        print(f"\nNext steps:")
        print(f"1. The enhanced brain_region_mapping.py is ready")
        print(f"2. Server.py has been updated to use AAL3 atlas")
        print(f"3. Reports now include detailed parietal and SMA analysis")
        print(f"4. You can test with real data using the server")
    else:
        print(f"\nPlease address the issues above before proceeding")
