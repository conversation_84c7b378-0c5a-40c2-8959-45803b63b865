"""
Brain region mapping utilities for stroke lesion analysis.
This module provides functions to map stroke lesions to anatomical brain regions
using the AAL (Automated Anatomical Labeling) atlas.
"""

import numpy as np
import nibabel as nib
import os
from typing import Dict, List, Tuple
from scipy import ndimage
from nibabel.processing import resample_to_output

# AAL3 Atlas Region Labels (partial list - most commonly affected in stroke)
AAL_REGIONS = {
    # Frontal regions
    1: "Precentral_L",
    2: "Precentral_R", 
    3: "Frontal_Sup_L",
    4: "Frontal_Sup_R",
    5: "Frontal_Sup_Orb_L",
    6: "Frontal_Sup_Orb_R",
    7: "Frontal_Mid_L",
    8: "Frontal_Mid_R",
    9: "Frontal_Mid_Orb_L",
    10: "Frontal_Mid_Orb_R",
    11: "Frontal_Inf_Oper_L",
    12: "Frontal_Inf_Oper_R",
    13: "Frontal_Inf_Tri_L",
    14: "Frontal_Inf_Tri_R",
    15: "Frontal_Inf_Orb_L",
    16: "Frontal_Inf_Orb_R",
    17: "Roland<PERSON>_Oper_L",
    18: "Rolandic_Oper_R",
    19: "Supp_Motor_Area_L",
    20: "Supp_Motor_Area_R",
    
    # Parietal regions
    21: "Frontal_Sup_Medial_L",
    22: "Frontal_Sup_Medial_R",
    23: "Frontal_Med_Orb_L",
    24: "Frontal_Med_Orb_R",
    25: "Rectus_L",
    26: "Rectus_R",
    27: "Insula_L",
    28: "Insula_R",
    29: "Cingulum_Ant_L",
    30: "Cingulum_Ant_R",
    31: "Cingulum_Mid_L",
    32: "Cingulum_Mid_R",
    33: "Cingulum_Post_L",
    34: "Cingulum_Post_R",
    35: "Hippocampus_L",
    36: "Hippocampus_R",
    37: "ParaHippocampal_L",
    38: "ParaHippocampal_R",
    39: "Amygdala_L",
    40: "Amygdala_R",
    41: "Calcarine_L",
    42: "Calcarine_R",
    43: "Cuneus_L",
    44: "Cuneus_R",
    45: "Lingual_L",
    46: "Lingual_R",
    47: "Occipital_Sup_L",
    48: "Occipital_Sup_R",
    49: "Occipital_Mid_L",
    50: "Occipital_Mid_R",
    51: "Occipital_Inf_L",
    52: "Occipital_Inf_R",
    53: "Fusiform_L",
    54: "Fusiform_R",
    55: "Postcentral_L",
    56: "Postcentral_R",
    57: "Parietal_Sup_L",
    58: "Parietal_Sup_R",
    59: "Parietal_Inf_L",
    60: "Parietal_Inf_R",
    61: "SupraMarginal_L",
    62: "SupraMarginal_R",
    63: "Angular_L",
    64: "Angular_R",
    65: "Precuneus_L",
    66: "Precuneus_R",
    
    # Temporal regions
    67: "Paracentral_Lobule_L",
    68: "Paracentral_Lobule_R",
    69: "Caudate_L",
    70: "Caudate_R",
    71: "Putamen_L",
    72: "Putamen_R",
    73: "Pallidum_L",
    74: "Pallidum_R",
    75: "Thalamus_L",
    76: "Thalamus_R",
    77: "Heschl_L",
    78: "Heschl_R",
    79: "Temporal_Sup_L",
    80: "Temporal_Sup_R",
    81: "Temporal_Pole_Sup_L",
    82: "Temporal_Pole_Sup_R",
    83: "Temporal_Mid_L",
    84: "Temporal_Mid_R",
    85: "Temporal_Pole_Mid_L",
    86: "Temporal_Pole_Mid_R",
    87: "Temporal_Inf_L",
    88: "Temporal_Inf_R",
}

# Simplified region groupings for clinical interpretation
REGION_GROUPS = {
    "Motor Areas": [1, 2, 19, 20, 55, 56, 67, 68],  # Precentral, SMA, Postcentral, Paracentral
    "Frontal Lobe": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 22, 23, 24, 25, 26],
    "Parietal Lobe": [57, 58, 59, 60, 61, 62, 63, 64, 65, 66],
    "Temporal Lobe": [77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88],
    "Occipital Lobe": [41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52],
    "Insula": [27, 28],
    "Cingulate": [29, 30, 31, 32, 33, 34],
    "Subcortical": [69, 70, 71, 72, 73, 74, 75, 76],  # Caudate, Putamen, Pallidum, Thalamus
    "Limbic": [35, 36, 37, 38, 39, 40],  # Hippocampus, ParaHippocampal, Amygdala
}


def create_simple_aal_atlas(template_path: str, output_path: str) -> str:
    """
    Create a simplified AAL-like atlas based on anatomical knowledge.
    This is a fallback when the actual AAL atlas is not available.
    
    Args:
        template_path: Path to the MNI152 template
        output_path: Path where to save the created atlas
        
    Returns:
        Path to the created atlas file
    """
    # Load the template to get the same dimensions and affine
    template_img = nib.load(template_path)
    template_data = template_img.get_fdata()
    
    # Create a simple atlas with basic regions
    # This is a very simplified version - in practice, you'd want the real AAL atlas
    atlas_data = np.zeros_like(template_data)
    
    # Define some basic regions based on MNI coordinates (very simplified)
    # These are rough approximations and should be replaced with actual AAL atlas
    x_size, y_size, z_size = template_data.shape
    
    # Left hemisphere motor areas (approximate)
    atlas_data[40:60, 20:40, 45:65] = 1  # Left Precentral
    atlas_data[35:55, 15:35, 50:70] = 19  # Left SMA
    
    # Right hemisphere motor areas (approximate)  
    atlas_data[40:60, 60:80, 45:65] = 2  # Right Precentral
    atlas_data[35:55, 65:85, 50:70] = 20  # Right SMA
    
    # Left parietal (approximate)
    atlas_data[60:80, 20:40, 45:65] = 57  # Left Superior Parietal
    
    # Right parietal (approximate)
    atlas_data[60:80, 60:80, 45:65] = 58  # Right Superior Parietal
    
    # Save the atlas
    atlas_img = nib.Nifti1Image(atlas_data, template_img.affine, template_img.header)
    nib.save(atlas_img, output_path)
    
    return output_path


def analyze_lesion_regions(lesion_mask_path: str, atlas_path: str = None,
                          template_path: str = None) -> Dict:
    """
    Analyze which brain regions are affected by the stroke lesion using AAL3 atlas.

    Args:
        lesion_mask_path: Path to the lesion segmentation mask
        atlas_path: Path to the AAL atlas (if None, will look for AAL3v1_1mm.nii.gz in same directory)
        template_path: Path to MNI152 template (for creating simple atlas if needed)

    Returns:
        Dictionary containing region analysis results
    """
    # Load lesion mask
    lesion_img = nib.load(lesion_mask_path)
    lesion_data = lesion_img.get_fdata()

    # Determine atlas path
    if atlas_path is None:
        # Look for AAL3v1_1mm.nii.gz in the flask-app directory (1mm resolution version)
        script_dir = os.path.dirname(os.path.abspath(__file__))
        atlas_path = os.path.join(script_dir, 'AAL3v1_1mm.nii.gz')

    # Create or load atlas
    if not os.path.exists(atlas_path):
        if template_path and os.path.exists(template_path):
            # Create a simple atlas as fallback
            atlas_dir = os.path.dirname(lesion_mask_path)
            fallback_atlas_path = os.path.join(atlas_dir, 'simple_aal_atlas.nii')
            create_simple_aal_atlas(template_path, fallback_atlas_path)
            atlas_path = fallback_atlas_path
        else:
            return {"error": "No AAL atlas available and no template provided"}

    try:
        # Load atlas
        atlas_img = nib.load(atlas_path)
        atlas_data = atlas_img.get_fdata()

        # Check dimensions and provide detailed information
        if lesion_data.shape != atlas_data.shape:
            print(f"Info: Lesion dimensions: {lesion_data.shape}, Atlas dimensions: {atlas_data.shape}")
            # If dimensions don't match, this should now be resolved with 1mm atlas
            return {"error": f"Lesion mask and atlas have different dimensions: {lesion_data.shape} vs {atlas_data.shape}. Please ensure both are in 1mm MNI152 space."}

        # Find overlapping regions
        lesion_binary = lesion_data > 0.5
        affected_regions = {}

        # Get voxel volume for volume calculations
        voxel_dims = lesion_img.header.get_zooms()
        voxel_volume = voxel_dims[0] * voxel_dims[1] * voxel_dims[2]

        # Analyze each region
        unique_labels = np.unique(atlas_data[atlas_data > 0])
        print(f"Found {len(unique_labels)} unique regions in atlas")

        for label in unique_labels:
            label = int(label)
            if label in AAL_REGIONS:
                region_mask = atlas_data == label
                overlap = np.logical_and(lesion_binary, region_mask)
                overlap_volume = np.sum(overlap) * voxel_volume

                if overlap_volume > 0:
                    region_total_volume = np.sum(region_mask) * voxel_volume
                    overlap_percentage = (overlap_volume / region_total_volume) * 100

                    affected_regions[label] = {
                        'name': AAL_REGIONS[label],
                        'overlap_volume_mm3': overlap_volume,
                        'region_total_volume_mm3': region_total_volume,
                        'overlap_percentage': overlap_percentage
                    }

        # Group by anatomical systems
        affected_groups = {}
        for group_name, region_ids in REGION_GROUPS.items():
            group_volume = 0
            group_regions = []

            for region_id in region_ids:
                if region_id in affected_regions:
                    group_volume += affected_regions[region_id]['overlap_volume_mm3']
                    group_regions.append(affected_regions[region_id]['name'])

            if group_volume > 0:
                affected_groups[group_name] = {
                    'total_volume_mm3': group_volume,
                    'affected_regions': group_regions
                }

        return {
            'affected_regions': affected_regions,
            'affected_groups': affected_groups,
            'total_lesion_volume_mm3': np.sum(lesion_binary) * voxel_volume,
            'atlas_used': atlas_path
        }

    except Exception as e:
        return {"error": f"Error loading or processing atlas: {str(e)}"}


def generate_region_report(region_analysis: Dict) -> str:
    """
    Generate a comprehensive human-readable report of affected brain regions.

    Args:
        region_analysis: Results from analyze_lesion_regions

    Returns:
        Formatted text report with clinical insights
    """
    if 'error' in region_analysis:
        return f"Error in region analysis: {region_analysis['error']}"

    report = []
    report.append("STROKE LESION - BRAIN REGION ANALYSIS REPORT")
    report.append("=" * 50)
    report.append("")

    # Atlas information
    if 'atlas_used' in region_analysis:
        atlas_name = os.path.basename(region_analysis['atlas_used'])
        report.append(f"Atlas used: {atlas_name}")
        report.append("")

    # Total lesion volume
    total_volume = region_analysis.get('total_lesion_volume_mm3', 0)
    total_volume_ml = total_volume / 1000  # Convert to mL
    report.append(f"Total Lesion Volume: {total_volume:.2f} mm³ ({total_volume_ml:.2f} mL)")
    report.append("")

    # Clinical significance assessment
    if total_volume_ml < 1:
        severity = "Small"
    elif total_volume_ml < 10:
        severity = "Moderate"
    elif total_volume_ml < 50:
        severity = "Large"
    else:
        severity = "Very Large"

    report.append(f"Lesion Size Classification: {severity}")
    report.append("")

    # Highlight key clinical areas first
    key_areas = ["Motor Areas", "Supplementary Motor Area", "Parietal Lobe", "Parietal Lobe - Superior",
                 "Parietal Lobe - Inferior", "Parietal Lobe - SupraMarginal", "Parietal Lobe - Angular"]

    affected_groups = region_analysis.get('affected_groups', {})

    # Key clinical findings
    report.append("KEY CLINICAL FINDINGS:")
    report.append("-" * 25)

    key_findings = []
    for area in key_areas:
        if area in affected_groups:
            volume = affected_groups[area]['total_volume_mm3']
            percentage = (volume / total_volume) * 100 if total_volume > 0 else 0
            key_findings.append(f"• {area}: {volume:.2f} mm³ ({percentage:.1f}% of lesion)")
            # Show specific regions for key areas
            for region in affected_groups[area]['affected_regions']:
                key_findings.append(f"  - {region}")

    if key_findings:
        report.extend(key_findings)
    else:
        report.append("• No involvement of primary motor or parietal areas detected")

    report.append("")

    # All affected anatomical systems
    if affected_groups:
        report.append("ALL AFFECTED ANATOMICAL SYSTEMS:")
        report.append("-" * 35)

        # Sort by volume (largest first)
        sorted_groups = sorted(affected_groups.items(),
                             key=lambda x: x[1]['total_volume_mm3'], reverse=True)

        for group_name, group_info in sorted_groups:
            volume = group_info['total_volume_mm3']
            percentage = (volume / total_volume) * 100 if total_volume > 0 else 0
            report.append(f"{group_name}: {volume:.2f} mm³ ({percentage:.1f}% of lesion)")

            # Show specific regions for smaller groups or key areas
            if group_name in key_areas or len(group_info['affected_regions']) <= 3:
                for region in group_info['affected_regions']:
                    report.append(f"  - {region}")
            else:
                # For areas with many regions, show count
                report.append(f"  - {len(group_info['affected_regions'])} regions affected")
        report.append("")

    # Detailed region analysis (top 10 most affected)
    if region_analysis.get('affected_regions'):
        report.append("DETAILED REGION ANALYSIS (Top 10 Most Affected):")
        report.append("-" * 45)

        # Sort by overlap volume (largest first)
        sorted_regions = sorted(
            region_analysis['affected_regions'].items(),
            key=lambda x: x[1]['overlap_volume_mm3'],
            reverse=True
        )

        # Show top 10
        for i, (region_id, region_info) in enumerate(sorted_regions[:10]):
            name = region_info['name']
            volume = region_info['overlap_volume_mm3']
            percentage = region_info['overlap_percentage']
            lesion_percentage = (volume / total_volume) * 100 if total_volume > 0 else 0

            report.append(f"{i+1}. {name}:")
            report.append(f"   Volume affected: {volume:.2f} mm³ ({lesion_percentage:.1f}% of total lesion)")
            report.append(f"   Percentage of this region affected: {percentage:.1f}%")
            report.append("")

        # Summary of remaining regions
        if len(sorted_regions) > 10:
            remaining_count = len(sorted_regions) - 10
            remaining_volume = sum(info['overlap_volume_mm3'] for _, info in sorted_regions[10:])
            remaining_percentage = (remaining_volume / total_volume) * 100 if total_volume > 0 else 0
            report.append(f"Additional {remaining_count} regions affected: {remaining_volume:.2f} mm³ ({remaining_percentage:.1f}% of lesion)")
            report.append("")

    # Clinical interpretation notes
    report.append("CLINICAL INTERPRETATION NOTES:")
    report.append("-" * 30)

    # Motor function assessment
    motor_affected = any(area in affected_groups for area in ["Motor Areas", "Supplementary Motor Area"])
    if motor_affected:
        report.append("• Motor function may be impaired due to involvement of motor areas")

    # Parietal function assessment
    parietal_affected = any("Parietal" in area for area in affected_groups.keys())
    if parietal_affected:
        report.append("• Parietal lobe involvement may affect spatial processing, attention, and sensory integration")

    # SMA specific
    if "Supplementary Motor Area" in affected_groups:
        report.append("• Supplementary Motor Area involvement may affect motor planning and coordination")

    # Language areas (if temporal/frontal involved)
    language_areas = ["Frontal Lobe", "Temporal Lobe"]
    language_affected = any(area in affected_groups for area in language_areas)
    if language_affected:
        report.append("• Language function assessment recommended due to frontal/temporal involvement")

    if not motor_affected and not parietal_affected:
        report.append("• Primary motor and parietal areas appear to be spared")

    report.append("")
    report.append("Note: This analysis is based on anatomical overlap and should be interpreted")
    report.append("in conjunction with clinical assessment and functional testing.")

    return "\n".join(report)
