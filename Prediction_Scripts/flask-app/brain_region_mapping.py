"""
Brain region mapping utilities for stroke lesion analysis.
This module provides functions to map stroke lesions to anatomical brain regions
using the AAL (Automated Anatomical Labeling) atlas.
"""

import numpy as np
import nibabel as nib
import os
from typing import Dict, List, Tuple
from scipy import ndimage
from nibabel.processing import resample_to_output

# AAL3 Atlas Region Labels - Complete list from AAL3v1.nii.txt
AAL_REGIONS = {
    # Frontal regions
    1: "Precentral_L",
    2: "Precentral_R",
    3: "Frontal_Sup_2_L",
    4: "Frontal_Sup_2_R",
    5: "Frontal_Mid_2_L",
    6: "Frontal_Mid_2_R",
    7: "Frontal_Inf_Oper_L",
    8: "Frontal_Inf_Oper_R",
    9: "Frontal_Inf_Tri_L",
    10: "Frontal_Inf_Tri_R",
    11: "Frontal_Inf_Orb_2_L",
    12: "Frontal_Inf_Orb_2_R",
    13: "<PERSON><PERSON>_Oper_<PERSON>",
    14: "<PERSON><PERSON>_Oper_R",
    15: "Supp_Motor_Area_L",
    16: "Supp_Motor_Area_R",
    17: "Olfactory_L",
    18: "Olfactory_R",
    19: "Frontal_Sup_Medial_L",
    20: "Frontal_Sup_Medial_R",
    21: "Frontal_Med_Orb_L",
    22: "Frontal_Med_Orb_R",
    23: "Rectus_L",
    24: "Rectus_R",
    25: "OFCmed_L",
    26: "OFCmed_R",
    27: "OFCant_L",
    28: "OFCant_R",
    29: "OFCpost_L",
    30: "OFCpost_R",
    31: "OFClat_L",
    32: "OFClat_R",

    # Insula and Cingulate
    33: "Insula_L",
    34: "Insula_R",
    35: "Cingulate_Ant_L",
    36: "Cingulate_Ant_R",
    37: "Cingulate_Mid_L",
    38: "Cingulate_Mid_R",
    39: "Cingulate_Post_L",
    40: "Cingulate_Post_R",

    # Limbic regions
    41: "Hippocampus_L",
    42: "Hippocampus_R",
    43: "ParaHippocampal_L",
    44: "ParaHippocampal_R",
    45: "Amygdala_L",
    46: "Amygdala_R",

    # Occipital regions
    47: "Calcarine_L",
    48: "Calcarine_R",
    49: "Cuneus_L",
    50: "Cuneus_R",
    51: "Lingual_L",
    52: "Lingual_R",
    53: "Occipital_Sup_L",
    54: "Occipital_Sup_R",
    55: "Occipital_Mid_L",
    56: "Occipital_Mid_R",
    57: "Occipital_Inf_L",
    58: "Occipital_Inf_R",
    59: "Fusiform_L",
    60: "Fusiform_R",

    # Parietal regions
    61: "Postcentral_L",
    62: "Postcentral_R",
    63: "Parietal_Sup_L",
    64: "Parietal_Sup_R",
    65: "Parietal_Inf_L",
    66: "Parietal_Inf_R",
    67: "SupraMarginal_L",
    68: "SupraMarginal_R",
    69: "Angular_L",
    70: "Angular_R",
    71: "Precuneus_L",
    72: "Precuneus_R",
    73: "Paracentral_Lobule_L",
    74: "Paracentral_Lobule_R",

    # Subcortical regions
    75: "Caudate_L",
    76: "Caudate_R",
    77: "Putamen_L",
    78: "Putamen_R",
    79: "Pallidum_L",
    80: "Pallidum_R",
    81: "Thalamus_L",
    82: "Thalamus_R",

    # Temporal regions
    83: "Heschl_L",
    84: "Heschl_R",
    85: "Temporal_Sup_L",
    86: "Temporal_Sup_R",
    87: "Temporal_Pole_Sup_L",
    88: "Temporal_Pole_Sup_R",
    89: "Temporal_Mid_L",
    90: "Temporal_Mid_R",
    91: "Temporal_Pole_Mid_L",
    92: "Temporal_Pole_Mid_R",
    93: "Temporal_Inf_L",
    94: "Temporal_Inf_R",

    # Cerebellum
    95: "Cerebellum_Crus1_L",
    96: "Cerebellum_Crus1_R",
    97: "Cerebellum_Crus2_L",
    98: "Cerebellum_Crus2_R",
    99: "Cerebellum_3_L",
    100: "Cerebellum_3_R",
    101: "Cerebellum_4_5_L",
    102: "Cerebellum_4_5_R",
    103: "Cerebellum_6_L",
    104: "Cerebellum_6_R",
    105: "Cerebellum_7b_L",
    106: "Cerebellum_7b_R",
    107: "Cerebellum_8_L",
    108: "Cerebellum_8_R",
    109: "Cerebellum_9_L",
    110: "Cerebellum_9_R",
    111: "Cerebellum_10_L",
    112: "Cerebellum_10_R",
    113: "Vermis_1_2",
    114: "Vermis_3",
    115: "Vermis_4_5",
    116: "Vermis_6",
    117: "Vermis_7",
    118: "Vermis_8",
    119: "Vermis_9",
    120: "Vermis_10",

    # Detailed Thalamic nuclei
    121: "Thal_AV_L",
    122: "Thal_AV_R",
    123: "Thal_LP_L",
    124: "Thal_LP_R",
    125: "Thal_VA_L",
    126: "Thal_VA_R",
    127: "Thal_VL_L",
    128: "Thal_VL_R",
    129: "Thal_VPL_L",
    130: "Thal_VPL_R",
    131: "Thal_IL_L",
    132: "Thal_IL_R",
    133: "Thal_Re_L",
    134: "Thal_Re_R",
    135: "Thal_MDm_L",
    136: "Thal_MDm_R",
    137: "Thal_MDl_L",
    138: "Thal_MDl_R",
    139: "Thal_LGN_L",
    140: "Thal_LGN_R",
    141: "Thal_MGN_L",
    142: "Thal_MGN_R",
    143: "Thal_PuI_L",
    144: "Thal_PuI_R",
    145: "Thal_PuM_L",
    146: "Thal_PuM_R",
    147: "Thal_PuA_L",
    148: "Thal_PuA_R",
    149: "Thal_PuL_L",
    150: "Thal_PuL_R",

    # Additional subcortical regions
    151: "ACC_sub_L",
    152: "ACC_sub_R",
    153: "ACC_pre_L",
    154: "ACC_pre_R",
    155: "ACC_sup_L",
    156: "ACC_sup_R",
    157: "N_Acc_L",
    158: "N_Acc_R",
    159: "VTA_L",
    160: "VTA_R",
    161: "SN_pc_L",
    162: "SN_pc_R",
    163: "SN_pr_L",
    164: "SN_pr_R",
    165: "Red_N_L",
    166: "Red_N_R",
    167: "LC_L",
    168: "LC_R",
    169: "Raphe_D",
    170: "Raphe_M",
}

# Enhanced region groupings for clinical interpretation based on AAL3
REGION_GROUPS = {
    "Motor Areas": [1, 2, 15, 16, 61, 62, 73, 74],  # Precentral, SMA, Postcentral, Paracentral
    "Supplementary Motor Area": [15, 16],  # Left and Right SMA - highlighted as requested
    "Frontal Lobe": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32],
    "Parietal Lobe": [63, 64, 65, 66, 67, 68, 69, 70, 71, 72],  # Superior, Inferior, SupraMarginal, Angular, Precuneus
    "Parietal Lobe - Superior": [63, 64],  # Superior parietal lobule
    "Parietal Lobe - Inferior": [65, 66],  # Inferior parietal lobule
    "Parietal Lobe - SupraMarginal": [67, 68],  # SupraMarginal gyrus
    "Parietal Lobe - Angular": [69, 70],  # Angular gyrus
    "Temporal Lobe": [83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94],
    "Occipital Lobe": [47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60],
    "Insula": [33, 34],
    "Cingulate Cortex": [35, 36, 37, 38, 39, 40, 151, 152, 153, 154, 155, 156],
    "Subcortical - Basal Ganglia": [75, 76, 77, 78, 79, 80],  # Caudate, Putamen, Pallidum
    "Subcortical - Thalamus": [81, 82] + list(range(121, 151)),  # Main thalamus + detailed nuclei
    "Limbic System": [41, 42, 43, 44, 45, 46],  # Hippocampus, ParaHippocampal, Amygdala
    "Cerebellum": list(range(95, 121)),  # All cerebellar regions
    "Brainstem": [159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170],  # VTA, SN, Red nucleus, etc.
}


def create_simple_aal_atlas(template_path: str, output_path: str) -> str:
    """
    Create a simplified AAL-like atlas based on anatomical knowledge.
    This is a fallback when the actual AAL atlas is not available.
    
    Args:
        template_path: Path to the MNI152 template
        output_path: Path where to save the created atlas
        
    Returns:
        Path to the created atlas file
    """
    # Load the template to get the same dimensions and affine
    template_img = nib.load(template_path)
    template_data = template_img.get_fdata()
    
    # Create a simple atlas with basic regions
    # This is a very simplified version - in practice, you'd want the real AAL atlas
    atlas_data = np.zeros_like(template_data)
    
    # Define some basic regions based on MNI coordinates (very simplified)
    # These are rough approximations and should be replaced with actual AAL atlas
    x_size, y_size, z_size = template_data.shape
    
    # Left hemisphere motor areas (approximate)
    atlas_data[40:60, 20:40, 45:65] = 1  # Left Precentral
    atlas_data[35:55, 15:35, 50:70] = 19  # Left SMA
    
    # Right hemisphere motor areas (approximate)  
    atlas_data[40:60, 60:80, 45:65] = 2  # Right Precentral
    atlas_data[35:55, 65:85, 50:70] = 20  # Right SMA
    
    # Left parietal (approximate)
    atlas_data[60:80, 20:40, 45:65] = 57  # Left Superior Parietal
    
    # Right parietal (approximate)
    atlas_data[60:80, 60:80, 45:65] = 58  # Right Superior Parietal
    
    # Save the atlas
    atlas_img = nib.Nifti1Image(atlas_data, template_img.affine, template_img.header)
    nib.save(atlas_img, output_path)
    
    return output_path


def analyze_lesion_regions(lesion_mask_path: str, atlas_path: str = None,
                          template_path: str = None) -> Dict:
    """
    Analyze which brain regions are affected by the stroke lesion using AAL3 atlas.

    Args:
        lesion_mask_path: Path to the lesion segmentation mask
        atlas_path: Path to the AAL atlas (if None, will look for AAL3v1.nii.gz in same directory)
        template_path: Path to MNI152 template (for creating simple atlas if needed)

    Returns:
        Dictionary containing region analysis results
    """
    # Load lesion mask
    lesion_img = nib.load(lesion_mask_path)
    lesion_data = lesion_img.get_fdata()

    # Determine atlas path
    if atlas_path is None:
        # Look for AAL3v1.nii.gz in the flask-app directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        atlas_path = os.path.join(script_dir, 'AAL3v1.nii.gz')

    # Create or load atlas
    if not os.path.exists(atlas_path):
        if template_path and os.path.exists(template_path):
            # Create a simple atlas as fallback
            atlas_dir = os.path.dirname(lesion_mask_path)
            fallback_atlas_path = os.path.join(atlas_dir, 'simple_aal_atlas.nii')
            create_simple_aal_atlas(template_path, fallback_atlas_path)
            atlas_path = fallback_atlas_path
        else:
            return {"error": "No AAL atlas available and no template provided"}

    try:
        # Load atlas
        atlas_img = nib.load(atlas_path)
        atlas_data = atlas_img.get_fdata()

        # Ensure same dimensions - if not, try to resample
        if lesion_data.shape != atlas_data.shape:
            print(f"Warning: Dimension mismatch - Lesion: {lesion_data.shape}, Atlas: {atlas_data.shape}")
            # For now, return error - in production, you might want to implement resampling
            return {"error": f"Lesion mask and atlas have different dimensions: {lesion_data.shape} vs {atlas_data.shape}"}

        # Find overlapping regions
        lesion_binary = lesion_data > 0.5
        affected_regions = {}

        # Get voxel volume for volume calculations
        voxel_dims = lesion_img.header.get_zooms()
        voxel_volume = voxel_dims[0] * voxel_dims[1] * voxel_dims[2]

        # Analyze each region
        unique_labels = np.unique(atlas_data[atlas_data > 0])

        for label in unique_labels:
            label = int(label)
            if label in AAL_REGIONS:
                region_mask = atlas_data == label
                overlap = np.logical_and(lesion_binary, region_mask)
                overlap_volume = np.sum(overlap) * voxel_volume

                if overlap_volume > 0:
                    region_total_volume = np.sum(region_mask) * voxel_volume
                    overlap_percentage = (overlap_volume / region_total_volume) * 100

                    affected_regions[label] = {
                        'name': AAL_REGIONS[label],
                        'overlap_volume_mm3': overlap_volume,
                        'region_total_volume_mm3': region_total_volume,
                        'overlap_percentage': overlap_percentage
                    }

        # Group by anatomical systems
        affected_groups = {}
        for group_name, region_ids in REGION_GROUPS.items():
            group_volume = 0
            group_regions = []

            for region_id in region_ids:
                if region_id in affected_regions:
                    group_volume += affected_regions[region_id]['overlap_volume_mm3']
                    group_regions.append(affected_regions[region_id]['name'])

            if group_volume > 0:
                affected_groups[group_name] = {
                    'total_volume_mm3': group_volume,
                    'affected_regions': group_regions
                }

        return {
            'affected_regions': affected_regions,
            'affected_groups': affected_groups,
            'total_lesion_volume_mm3': np.sum(lesion_binary) * voxel_volume,
            'atlas_used': atlas_path
        }

    except Exception as e:
        return {"error": f"Error loading or processing atlas: {str(e)}"}


def generate_region_report(region_analysis: Dict) -> str:
    """
    Generate a comprehensive human-readable report of affected brain regions.

    Args:
        region_analysis: Results from analyze_lesion_regions

    Returns:
        Formatted text report with clinical insights
    """
    if 'error' in region_analysis:
        return f"Error in region analysis: {region_analysis['error']}"

    report = []
    report.append("STROKE LESION - BRAIN REGION ANALYSIS REPORT")
    report.append("=" * 50)
    report.append("")

    # Atlas information
    if 'atlas_used' in region_analysis:
        atlas_name = os.path.basename(region_analysis['atlas_used'])
        report.append(f"Atlas used: {atlas_name}")
        report.append("")

    # Total lesion volume
    total_volume = region_analysis.get('total_lesion_volume_mm3', 0)
    total_volume_ml = total_volume / 1000  # Convert to mL
    report.append(f"Total Lesion Volume: {total_volume:.2f} mm³ ({total_volume_ml:.2f} mL)")
    report.append("")

    # Clinical significance assessment
    if total_volume_ml < 1:
        severity = "Small"
    elif total_volume_ml < 10:
        severity = "Moderate"
    elif total_volume_ml < 50:
        severity = "Large"
    else:
        severity = "Very Large"

    report.append(f"Lesion Size Classification: {severity}")
    report.append("")

    # Highlight key clinical areas first
    key_areas = ["Motor Areas", "Supplementary Motor Area", "Parietal Lobe", "Parietal Lobe - Superior",
                 "Parietal Lobe - Inferior", "Parietal Lobe - SupraMarginal", "Parietal Lobe - Angular"]

    affected_groups = region_analysis.get('affected_groups', {})

    # Key clinical findings
    report.append("KEY CLINICAL FINDINGS:")
    report.append("-" * 25)

    key_findings = []
    for area in key_areas:
        if area in affected_groups:
            volume = affected_groups[area]['total_volume_mm3']
            percentage = (volume / total_volume) * 100 if total_volume > 0 else 0
            key_findings.append(f"• {area}: {volume:.2f} mm³ ({percentage:.1f}% of lesion)")

    if key_findings:
        report.extend(key_findings)
    else:
        report.append("• No involvement of primary motor or parietal areas detected")

    report.append("")

    # All affected anatomical systems
    if affected_groups:
        report.append("ALL AFFECTED ANATOMICAL SYSTEMS:")
        report.append("-" * 35)

        # Sort by volume (largest first)
        sorted_groups = sorted(affected_groups.items(),
                             key=lambda x: x[1]['total_volume_mm3'], reverse=True)

        for group_name, group_info in sorted_groups:
            volume = group_info['total_volume_mm3']
            percentage = (volume / total_volume) * 100 if total_volume > 0 else 0
            report.append(f"{group_name}: {volume:.2f} mm³ ({percentage:.1f}% of lesion)")

            # Show specific regions for key areas
            if group_name in key_areas or len(group_info['affected_regions']) <= 3:
                for region in group_info['affected_regions']:
                    report.append(f"  - {region}")
            else:
                # For areas with many regions, show count
                report.append(f"  - {len(group_info['affected_regions'])} regions affected")
        report.append("")

    # Detailed region analysis (top 10 most affected)
    if region_analysis.get('affected_regions'):
        report.append("DETAILED REGION ANALYSIS (Top 10 Most Affected):")
        report.append("-" * 45)

        # Sort by overlap volume (largest first)
        sorted_regions = sorted(
            region_analysis['affected_regions'].items(),
            key=lambda x: x[1]['overlap_volume_mm3'],
            reverse=True
        )

        # Show top 10
        for i, (region_id, region_info) in enumerate(sorted_regions[:10]):
            name = region_info['name']
            volume = region_info['overlap_volume_mm3']
            percentage = region_info['overlap_percentage']
            lesion_percentage = (volume / total_volume) * 100 if total_volume > 0 else 0

            report.append(f"{i+1}. {name}:")
            report.append(f"   Volume affected: {volume:.2f} mm³ ({lesion_percentage:.1f}% of total lesion)")
            report.append(f"   Percentage of this region affected: {percentage:.1f}%")
            report.append("")

        # Summary of remaining regions
        if len(sorted_regions) > 10:
            remaining_count = len(sorted_regions) - 10
            remaining_volume = sum(info['overlap_volume_mm3'] for _, info in sorted_regions[10:])
            remaining_percentage = (remaining_volume / total_volume) * 100 if total_volume > 0 else 0
            report.append(f"Additional {remaining_count} regions affected: {remaining_volume:.2f} mm³ ({remaining_percentage:.1f}% of lesion)")
            report.append("")

    # Clinical interpretation notes
    report.append("CLINICAL INTERPRETATION NOTES:")
    report.append("-" * 30)

    # Motor function assessment
    motor_affected = any(area in affected_groups for area in ["Motor Areas", "Supplementary Motor Area"])
    if motor_affected:
        report.append("• Motor function may be impaired due to involvement of motor areas")

    # Parietal function assessment
    parietal_affected = any("Parietal" in area for area in affected_groups.keys())
    if parietal_affected:
        report.append("• Parietal lobe involvement may affect spatial processing, attention, and sensory integration")

    # SMA specific
    if "Supplementary Motor Area" in affected_groups:
        report.append("• Supplementary Motor Area involvement may affect motor planning and coordination")

    # Language areas (if temporal/frontal involved)
    language_areas = ["Frontal Lobe", "Temporal Lobe"]
    language_affected = any(area in affected_groups for area in language_areas)
    if language_affected:
        report.append("• Language function assessment recommended due to frontal/temporal involvement")

    if not motor_affected and not parietal_affected:
        report.append("• Primary motor and parietal areas appear to be spared")

    report.append("")
    report.append("Note: This analysis is based on anatomical overlap and should be interpreted")
    report.append("in conjunction with clinical assessment and functional testing.")

    return "\n".join(report)
