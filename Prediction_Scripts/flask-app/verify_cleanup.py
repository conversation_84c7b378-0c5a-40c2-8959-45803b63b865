"""
Verification script to confirm PDF functionality has been removed and cleanup is complete.
"""

import os
import sys

def verify_pdf_removal():
    """Verify that PDF functionality has been removed from brain_region_mapping.py"""
    print("Verifying PDF Functionality Removal...")
    print("=" * 40)
    
    try:
        with open("brain_region_mapping.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check that PDF imports are removed
        pdf_imports = [
            "from reportlab",
            "import reportlab",
            "PDF_AVAILABLE",
            "from datetime import datetime"
        ]
        
        pdf_removed = True
        for import_text in pdf_imports:
            if import_text in content:
                print(f"❌ Found PDF import: {import_text}")
                pdf_removed = False
        
        if pdf_removed:
            print("✅ All PDF imports removed")
        
        # Check that PDF functions are removed
        pdf_functions = [
            "def generate_pdf_report",
            "def _add_analysis_content_to_pdf",
            "def _add_detailed_analysis_to_pdf", 
            "def _add_clinical_interpretation_to_pdf",
            "def generate_report_with_pdf"
        ]
        
        functions_removed = True
        for func_text in pdf_functions:
            if func_text in content:
                print(f"❌ Found PDF function: {func_text}")
                functions_removed = False
        
        if functions_removed:
            print("✅ All PDF functions removed")
        
        return pdf_removed and functions_removed
        
    except Exception as e:
        print(f"❌ Error checking brain_region_mapping.py: {e}")
        return False

def verify_server_cleanup():
    """Verify that server.py has been cleaned up"""
    print("\nVerifying Server Cleanup...")
    print("=" * 25)
    
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check that PDF imports are removed
        if "PDF_AVAILABLE" in content:
            print("❌ PDF_AVAILABLE still found in server.py")
            return False
        else:
            print("✅ PDF_AVAILABLE removed from server.py")
        
        if "generate_report_with_pdf" in content:
            print("❌ generate_report_with_pdf still found in server.py")
            return False
        else:
            print("✅ generate_report_with_pdf removed from server.py")
        
        # Check function signature
        if "patient_info=None, generate_pdf=True" in content:
            print("❌ PDF parameters still in function signature")
            return False
        else:
            print("✅ PDF parameters removed from function signature")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking server.py: {e}")
        return False

def verify_test_files_removed():
    """Verify that test files have been removed"""
    print("\nVerifying Test Files Removal...")
    print("=" * 30)
    
    test_files = [
        "test_pdf_generation.py",
        "install_pdf_support.py", 
        "test_resampling.py",
        "test_aal3_1mm.py",
        "test_resampling_fix.py",
        "verify_1mm_atlas.py",
        "verify_resampling_fix.py",
        "simple_verify.py",
        "PDF_REPORT_SUMMARY.md",
        "SOLUTION_SUMMARY.md",
        "DIMENSION_MISMATCH_SOLUTION.md",
        "NIBABEL_ERROR_FIX.md",
        "resampling_usage_example.py",
        "basic_test.py",
        "minimal_test.py",
        "simple_test.py"
    ]
    
    all_removed = True
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"❌ Test file still exists: {test_file}")
            all_removed = False
        else:
            print(f"✅ Removed: {test_file}")
    
    return all_removed

def verify_core_functionality():
    """Verify that core functionality still works"""
    print("\nVerifying Core Functionality...")
    print("=" * 30)
    
    try:
        # Test basic imports
        sys.path.insert(0, os.getcwd())
        from brain_region_mapping import analyze_lesion_regions, generate_region_report, AAL_REGIONS, REGION_GROUPS
        
        print("✅ Core imports successful")
        print(f"✅ AAL_REGIONS contains {len(AAL_REGIONS)} regions")
        print(f"✅ REGION_GROUPS contains {len(REGION_GROUPS)} groups")
        
        # Check key regions
        key_regions = [15, 16, 57, 58, 59, 60, 61, 62, 63, 64]  # SMA and parietal
        missing_regions = [r for r in key_regions if r not in AAL_REGIONS]
        
        if not missing_regions:
            print("✅ All key regions (SMA, parietal) are defined")
        else:
            print(f"❌ Missing key regions: {missing_regions}")
            return False
        
        # Test server import
        from server import analyze_stroke_regions
        print("✅ Server function import successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Core functionality test failed: {e}")
        return False

def check_remaining_files():
    """Check what files remain in the directory"""
    print("\nRemaining Files...")
    print("=" * 20)
    
    files = [f for f in os.listdir('.') if os.path.isfile(f)]
    
    core_files = [
        "brain_region_mapping.py",
        "server.py", 
        "istroke.py",
        "test_brain_regions.py",
        "README_brain_regions.md"
    ]
    
    data_files = [
        "AAL3v1_1mm.nii.gz",
        "AAL3v1_1mm.nii.txt",
        "MNI152_T1_1mm.nii",
        "MNI152_T1_1mm_brain_mask.nii"
    ]
    
    print("Core files:")
    for file in core_files:
        if file in files:
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} missing")
    
    print("\nData files:")
    for file in data_files:
        if file in files:
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} missing")
    
    print(f"\nTotal files in directory: {len(files)}")
    return True

def main():
    """Run all verification checks"""
    print("PDF Removal and Cleanup Verification")
    print("=" * 40)
    
    # Test 1: Verify PDF removal
    pdf_removed = verify_pdf_removal()
    
    # Test 2: Verify server cleanup
    server_cleaned = verify_server_cleanup()
    
    # Test 3: Verify test files removed
    tests_removed = verify_test_files_removed()
    
    # Test 4: Verify core functionality
    core_works = verify_core_functionality()
    
    # Test 5: Check remaining files
    files_checked = check_remaining_files()
    
    print("\n" + "=" * 40)
    print("VERIFICATION SUMMARY")
    print("=" * 40)
    
    checks = [
        ("PDF Functionality Removed", pdf_removed),
        ("Server Cleanup", server_cleaned),
        ("Test Files Removed", tests_removed),
        ("Core Functionality", core_works),
        ("File Check", files_checked),
    ]
    
    all_passed = True
    for check_name, passed in checks:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{check_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 CLEANUP VERIFICATION SUCCESSFUL!")
        print("\nCleanup Summary:")
        print("✅ PDF functionality completely removed")
        print("✅ All test files deleted")
        print("✅ Server code cleaned up")
        print("✅ Core functionality preserved")
        print("✅ Only essential files remain")
        
        print("\nYour system now has:")
        print("• Enhanced brain region analysis (AAL3 with 170 regions)")
        print("• Automatic dimension mismatch resolution")
        print("• Supplementary Motor Area tracking")
        print("• Enhanced parietal lobe analysis (4 subdivisions)")
        print("• Text-based comprehensive reports")
        print("• Clean, minimal codebase")
    else:
        print("❌ CLEANUP VERIFICATION FAILED")
        print("Some issues were found - check the details above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
