"""
Test script for brain region mapping functionality.
This script tests the brain region analysis without running the full prediction pipeline.
"""

import os
import sys
import numpy as np
import nibabel as nib
from brain_region_mapping import analyze_lesion_regions, generate_region_report, create_simple_aal_atlas

def create_test_lesion_mask(template_path, output_path):
    """
    Create a test lesion mask for testing purposes.
    """
    # Load template to get dimensions and affine
    template_img = nib.load(template_path)
    template_data = template_img.get_fdata()
    
    # Create a simple lesion mask (small sphere in motor area)
    lesion_data = np.zeros_like(template_data)
    
    # Create a small lesion in the left motor area (approximate coordinates)
    center_x, center_y, center_z = 50, 30, 55
    radius = 5
    
    x_size, y_size, z_size = template_data.shape
    
    for x in range(max(0, center_x - radius), min(x_size, center_x + radius)):
        for y in range(max(0, center_y - radius), min(y_size, center_y + radius)):
            for z in range(max(0, center_z - radius), min(z_size, center_z + radius)):
                distance = np.sqrt((x - center_x)**2 + (y - center_y)**2 + (z - center_z)**2)
                if distance <= radius:
                    lesion_data[x, y, z] = 1
    
    # Save the lesion mask
    lesion_img = nib.Nifti1Image(lesion_data, template_img.affine, template_img.header)
    nib.save(lesion_img, output_path)
    
    return output_path

def test_brain_region_analysis():
    """
    Test the brain region analysis functionality.
    """
    print("Testing Brain Region Analysis...")
    print("=" * 50)
    
    # Paths
    template_path = r"D:/Stroke/Stroke/Prediction_Scripts/flask-app/MNI152_T1_1mm.nii"
    test_lesion_path = "test_lesion_mask.nii"
    
    # Check if template exists
    if not os.path.exists(template_path):
        print(f"Error: Template file not found at {template_path}")
        return False
    
    try:
        # Create a test lesion mask
        print("Creating test lesion mask...")
        create_test_lesion_mask(template_path, test_lesion_path)
        print(f"Test lesion mask created: {test_lesion_path}")
        
        # Test the region analysis
        print("\nRunning brain region analysis...")
        region_analysis = analyze_lesion_regions(
            lesion_mask_path=test_lesion_path,
            atlas_path=None,  # Will create simple atlas
            template_path=template_path
        )
        
        # Generate report
        print("\nGenerating report...")
        report = generate_region_report(region_analysis)
        
        # Display results
        print("\n" + "=" * 50)
        print("BRAIN REGION ANALYSIS RESULTS")
        print("=" * 50)
        print(report)
        
        # Save results to file
        with open("test_brain_region_analysis.txt", "w", encoding="utf-8") as f:
            f.write("Test Brain Region Analysis Results\n")
            f.write("=" * 50 + "\n\n")
            f.write(report)
        
        print(f"\nResults saved to: test_brain_region_analysis.txt")
        
        # Clean up test files
        if os.path.exists(test_lesion_path):
            os.remove(test_lesion_path)
        if os.path.exists("simple_aal_atlas.nii"):
            os.remove("simple_aal_atlas.nii")
        
        return True
        
    except Exception as e:
        print(f"Error during testing: {str(e)}")
        return False

def test_atlas_creation():
    """
    Test the atlas creation functionality.
    """
    print("\nTesting Atlas Creation...")
    print("=" * 30)
    
    template_path = r"D:/Stroke/Stroke/Prediction_Scripts/flask-app/MNI152_T1_1mm.nii"
    atlas_path = "test_atlas.nii"
    
    if not os.path.exists(template_path):
        print(f"Error: Template file not found at {template_path}")
        return False
    
    try:
        # Create atlas
        print("Creating simple AAL atlas...")
        create_simple_aal_atlas(template_path, atlas_path)
        
        # Load and check atlas
        atlas_img = nib.load(atlas_path)
        atlas_data = atlas_img.get_fdata()
        
        unique_labels = np.unique(atlas_data)
        print(f"Atlas created successfully!")
        print(f"Atlas dimensions: {atlas_data.shape}")
        print(f"Unique labels in atlas: {unique_labels}")
        
        # Clean up
        if os.path.exists(atlas_path):
            os.remove(atlas_path)
        
        return True
        
    except Exception as e:
        print(f"Error creating atlas: {str(e)}")
        return False

if __name__ == "__main__":
    print("Brain Region Mapping Test Suite")
    print("=" * 50)
    
    # Test atlas creation
    atlas_success = test_atlas_creation()
    
    # Test brain region analysis
    analysis_success = test_brain_region_analysis()
    
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Atlas Creation: {'PASSED' if atlas_success else 'FAILED'}")
    print(f"Region Analysis: {'PASSED' if analysis_success else 'FAILED'}")
    
    if atlas_success and analysis_success:
        print("\nAll tests PASSED! Brain region mapping is ready to use.")
    else:
        print("\nSome tests FAILED. Please check the error messages above.")
