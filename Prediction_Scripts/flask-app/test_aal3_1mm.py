"""
Test script for the updated AAL3 1mm brain region mapping functionality.
This script tests the enhanced brain region analysis with the 1mm resolution AAL3 atlas.
"""

import os
import sys
import numpy as np
import nibabel as nib
from brain_region_mapping import analyze_lesion_regions, generate_region_report, AAL_REGIONS, REGION_GROUPS

def test_aal3_1mm_atlas_loading():
    """
    Test if the AAL3 1mm atlas can be loaded properly.
    """
    print("Testing AAL3 1mm Atlas Loading...")
    print("=" * 40)
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    aal_atlas_path = os.path.join(script_dir, 'AAL3v1_1mm.nii.gz')
    
    if not os.path.exists(aal_atlas_path):
        print(f"❌ ERROR: AAL3 1mm atlas not found at {aal_atlas_path}")
        return False
    
    try:
        # Load the atlas
        atlas_img = nib.load(aal_atlas_path)
        atlas_data = atlas_img.get_fdata()
        
        # Get unique labels
        unique_labels = np.unique(atlas_data[atlas_data > 0])
        
        print(f"✅ AAL3 1mm atlas loaded successfully!")
        print(f"   Atlas dimensions: {atlas_data.shape}")
        print(f"   Atlas voxel size: {atlas_img.header.get_zooms()}")
        print(f"   Number of unique regions: {len(unique_labels)}")
        print(f"   Label range: {int(unique_labels.min())} - {int(unique_labels.max())}")
        
        # Check how many labels we have definitions for
        defined_labels = [label for label in unique_labels if int(label) in AAL_REGIONS]
        print(f"   Regions with definitions: {len(defined_labels)}/{len(unique_labels)}")
        
        # Show some example regions
        print(f"   Example regions:")
        for i, label in enumerate(unique_labels[:5]):
            label_int = int(label)
            region_name = AAL_REGIONS.get(label_int, f"Unknown_{label_int}")
            print(f"     Label {label_int}: {region_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR loading AAL3 1mm atlas: {str(e)}")
        return False

def create_test_lesion_mask_1mm(template_path, output_path):
    """
    Create a test lesion mask that matches 1mm MNI152 space.
    """
    # Load template to get dimensions and affine
    template_img = nib.load(template_path)
    template_data = template_img.get_fdata()
    
    print(f"Template dimensions: {template_data.shape}")
    print(f"Template voxel size: {template_img.header.get_zooms()}")
    
    # Create a lesion mask
    lesion_data = np.zeros_like(template_data)
    
    # Create multiple small lesions in different areas
    # These coordinates are for 1mm MNI152 space (193, 229, 193)
    
    # Left motor area lesion (around precentral gyrus)
    lesion_data[85:95, 105:115, 95:105] = 1
    
    # Left parietal lesion (around superior parietal)
    lesion_data[110:120, 105:115, 95:105] = 1
    
    # Small SMA lesion (supplementary motor area)
    lesion_data[95:100, 115:120, 100:105] = 1
    
    # Save the lesion mask
    lesion_img = nib.Nifti1Image(lesion_data, template_img.affine, template_img.header)
    nib.save(lesion_img, output_path)
    
    # Calculate lesion volume
    voxel_dims = template_img.header.get_zooms()
    voxel_volume = voxel_dims[0] * voxel_dims[1] * voxel_dims[2]
    lesion_volume = np.sum(lesion_data > 0) * voxel_volume
    print(f"Created test lesion with volume: {lesion_volume:.2f} mm³")
    
    return output_path

def test_dimension_compatibility():
    """
    Test dimension compatibility between template and atlas.
    """
    print("\nTesting Dimension Compatibility...")
    print("=" * 40)
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    template_path = r"D:/Stroke/Stroke/Prediction_Scripts/flask-app/MNI152_T1_1mm.nii"
    aal_atlas_path = os.path.join(script_dir, 'AAL3v1_1mm.nii.gz')
    
    if not os.path.exists(template_path):
        print(f"❌ Template not found: {template_path}")
        return False
    
    if not os.path.exists(aal_atlas_path):
        print(f"❌ Atlas not found: {aal_atlas_path}")
        return False
    
    try:
        # Load both files
        template_img = nib.load(template_path)
        atlas_img = nib.load(aal_atlas_path)
        
        template_shape = template_img.get_fdata().shape
        atlas_shape = atlas_img.get_fdata().shape
        
        template_voxel = template_img.header.get_zooms()
        atlas_voxel = atlas_img.header.get_zooms()
        
        print(f"Template dimensions: {template_shape}")
        print(f"Template voxel size: {template_voxel}")
        print(f"Atlas dimensions: {atlas_shape}")
        print(f"Atlas voxel size: {atlas_voxel}")
        
        if template_shape == atlas_shape:
            print("✅ Dimensions match perfectly!")
            return True
        else:
            print("❌ Dimension mismatch detected!")
            return False
            
    except Exception as e:
        print(f"❌ Error checking dimensions: {str(e)}")
        return False

def test_full_analysis_pipeline_1mm():
    """
    Test the complete analysis pipeline with AAL3 1mm.
    """
    print("\nTesting Full Analysis Pipeline with 1mm Atlas...")
    print("=" * 50)
    
    # Paths
    template_path = r"D:/Stroke/Stroke/Prediction_Scripts/flask-app/MNI152_T1_1mm.nii"
    test_lesion_path = "test_lesion_1mm.nii"
    
    # Check if template exists
    if not os.path.exists(template_path):
        print(f"❌ ERROR: Template file not found at {template_path}")
        return False
    
    try:
        # Create a test lesion mask
        print("Creating test lesion mask...")
        create_test_lesion_mask_1mm(template_path, test_lesion_path)
        print(f"✅ Test lesion mask created: {test_lesion_path}")
        
        # Test the region analysis with AAL3 1mm
        print("\nRunning brain region analysis with AAL3 1mm...")
        region_analysis = analyze_lesion_regions(
            lesion_mask_path=test_lesion_path,
            atlas_path=None,  # Will automatically find AAL3v1_1mm.nii.gz
            template_path=template_path
        )
        
        if 'error' in region_analysis:
            print(f"❌ ERROR in analysis: {region_analysis['error']}")
            return False
        
        # Generate report
        print("Generating comprehensive report...")
        report = generate_region_report(region_analysis)
        
        # Display results
        print("\n" + "=" * 60)
        print("BRAIN REGION ANALYSIS RESULTS (AAL3 1mm)")
        print("=" * 60)
        print(report)
        
        # Save results to file
        with open("test_aal3_1mm_analysis_results.txt", "w", encoding="utf-8") as f:
            f.write("Test AAL3 1mm Brain Region Analysis Results\n")
            f.write("=" * 60 + "\n\n")
            f.write(report)
        
        print(f"\n✅ Results saved to: test_aal3_1mm_analysis_results.txt")
        
        # Summary statistics
        affected_regions = region_analysis.get('affected_regions', {})
        affected_groups = region_analysis.get('affected_groups', {})
        
        print(f"\nSUMMARY STATISTICS:")
        print(f"- Total regions affected: {len(affected_regions)}")
        print(f"- Anatomical groups affected: {len(affected_groups)}")
        print(f"- Total lesion volume: {region_analysis.get('total_lesion_volume_mm3', 0):.2f} mm³")
        print(f"- Atlas used: {os.path.basename(region_analysis.get('atlas_used', 'Unknown'))}")
        
        # Check for key areas
        key_areas = ["Motor Areas", "Supplementary Motor Area", "Parietal Lobe"]
        for area in key_areas:
            if area in affected_groups:
                volume = affected_groups[area]['total_volume_mm3']
                print(f"- {area}: {volume:.2f} mm³")
        
        # Clean up test files
        if os.path.exists(test_lesion_path):
            os.remove(test_lesion_path)
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR during testing: {str(e)}")
        return False

def main():
    """
    Run all tests for the AAL3 1mm integration.
    """
    print("AAL3 1mm Brain Region Mapping Integration Test Suite")
    print("=" * 60)
    
    # Test 1: AAL3 1mm atlas loading
    atlas_success = test_aal3_1mm_atlas_loading()
    
    # Test 2: Dimension compatibility
    dimension_success = test_dimension_compatibility()
    
    # Test 3: Full analysis pipeline
    analysis_success = test_full_analysis_pipeline_1mm()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"AAL3 1mm Atlas Loading: {'✅ PASSED' if atlas_success else '❌ FAILED'}")
    print(f"Dimension Compatibility: {'✅ PASSED' if dimension_success else '❌ FAILED'}")
    print(f"Full Analysis Pipeline: {'✅ PASSED' if analysis_success else '❌ FAILED'}")
    
    if atlas_success and dimension_success and analysis_success:
        print("\n🎉 All tests PASSED! AAL3 1mm integration is working correctly.")
        print("\nKey improvements:")
        print("- ✅ 1mm resolution AAL3 atlas eliminates dimension mismatch")
        print("- ✅ Enhanced parietal lobe subdivision analysis")
        print("- ✅ Dedicated supplementary motor area tracking")
        print("- ✅ Comprehensive clinical interpretation")
        print("- ✅ Detailed volume and percentage reporting")
        print("\nThe dimension mismatch issue has been resolved!")
    else:
        print("\n❌ Some tests FAILED. Please check the error messages above.")
        
    return atlas_success and dimension_success and analysis_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
