"""
Test script for PDF report generation functionality.
"""

import os
import sys
from datetime import datetime

def test_pdf_imports():
    """Test if PDF generation dependencies are available."""
    print("Testing PDF Generation Dependencies...")
    print("=" * 40)
    
    try:
        from brain_region_mapping import PDF_AVAILABLE, generate_pdf_report, generate_report_with_pdf
        
        if PDF_AVAILABLE:
            print("✅ reportlab is available")
            print("✅ PDF generation functions imported successfully")
            return True
        else:
            print("❌ reportlab is not available")
            print("   Install with: pip install reportlab")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_pdf_generation_with_mock_data():
    """Test PDF generation with mock analysis data."""
    print("\nTesting PDF Generation with Mock Data...")
    print("=" * 45)
    
    try:
        from brain_region_mapping import generate_pdf_report, generate_report_with_pdf, PDF_AVAILABLE
        
        if not PDF_AVAILABLE:
            print("❌ PDF generation not available - skipping test")
            return False
        
        # Create mock analysis data
        mock_analysis = {
            'total_lesion_volume_mm3': 1234.56,
            'atlas_used': 'AAL3v1_1mm.nii.gz',
            'affected_regions': {
                15: {
                    'name': 'Supp_Motor_Area_L',
                    'overlap_volume_mm3': 156.78,
                    'region_total_volume_mm3': 500.0,
                    'overlap_percentage': 31.4
                },
                57: {
                    'name': 'Parietal_Sup_L',
                    'overlap_volume_mm3': 234.56,
                    'region_total_volume_mm3': 800.0,
                    'overlap_percentage': 29.3
                },
                1: {
                    'name': 'Precentral_L',
                    'overlap_volume_mm3': 345.67,
                    'region_total_volume_mm3': 1200.0,
                    'overlap_percentage': 28.8
                }
            },
            'affected_groups': {
                'Motor Areas': {
                    'total_volume_mm3': 502.45,
                    'affected_regions': ['Precentral_L', 'Supp_Motor_Area_L']
                },
                'Supplementary Motor Area': {
                    'total_volume_mm3': 156.78,
                    'affected_regions': ['Supp_Motor_Area_L']
                },
                'Parietal Lobe - Superior': {
                    'total_volume_mm3': 234.56,
                    'affected_regions': ['Parietal_Sup_L']
                }
            }
        }
        
        # Mock patient information
        patient_info = {
            'Patient ID': 'TEST001',
            'Age': '65',
            'Gender': 'Male',
            'Scan Date': '2024-01-15',
            'Clinical Notes': 'Left hemisphere stroke'
        }
        
        # Test PDF generation
        print("Generating test PDF report...")
        pdf_path = "test_stroke_report.pdf"
        
        result_path = generate_pdf_report(mock_analysis, pdf_path, patient_info)
        
        if os.path.exists(result_path):
            file_size = os.path.getsize(result_path) / 1024  # KB
            print(f"✅ PDF generated successfully: {result_path}")
            print(f"   File size: {file_size:.1f} KB")
            
            # Test combined report generation
            print("\nTesting combined report generation...")
            reports = generate_report_with_pdf(mock_analysis, ".", patient_info)
            
            if reports['pdf_report_path'] and os.path.exists(reports['pdf_report_path']):
                print(f"✅ Combined reports generated successfully")
                print(f"   Text report: {reports['text_report_path']}")
                print(f"   PDF report: {reports['pdf_report_path']}")
                
                # Clean up test files
                for path in [pdf_path, reports['text_report_path'], reports['pdf_report_path']]:
                    if path and os.path.exists(path):
                        os.remove(path)
                        print(f"   Cleaned up: {os.path.basename(path)}")
                
                return True
            else:
                print("❌ Combined report generation failed")
                return False
        else:
            print("❌ PDF file was not created")
            return False
            
    except Exception as e:
        print(f"❌ Error during PDF generation test: {e}")
        return False

def test_server_integration():
    """Test server integration with PDF generation."""
    print("\nTesting Server Integration...")
    print("=" * 30)
    
    try:
        from server import analyze_stroke_regions, PDF_AVAILABLE
        
        print(f"PDF availability in server: {'✅ Available' if PDF_AVAILABLE else '❌ Not available'}")
        
        # Check if the function signature is updated
        import inspect
        sig = inspect.signature(analyze_stroke_regions)
        params = list(sig.parameters.keys())
        
        expected_params = ['mask_file_path', 'template_path', 'patient_info', 'generate_pdf']
        
        if all(param in params for param in expected_params):
            print("✅ Server function signature updated correctly")
            print(f"   Parameters: {params}")
            return True
        else:
            print("❌ Server function signature not updated")
            print(f"   Expected: {expected_params}")
            print(f"   Found: {params}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing server integration: {e}")
        return False

def check_reportlab_installation():
    """Check if reportlab can be installed."""
    print("\nChecking reportlab Installation...")
    print("=" * 35)
    
    try:
        import reportlab
        print(f"✅ reportlab is already installed")
        print(f"   Version: {reportlab.Version}")
        return True
    except ImportError:
        print("❌ reportlab is not installed")
        print("\nTo install reportlab, run:")
        print("   pip install reportlab")
        print("\nOr if using conda:")
        print("   conda install -c conda-forge reportlab")
        return False

def main():
    """Run all PDF generation tests."""
    print("PDF Report Generation Test Suite")
    print("=" * 40)
    
    # Test 1: Check reportlab installation
    reportlab_ok = check_reportlab_installation()
    
    # Test 2: Test PDF imports
    imports_ok = test_pdf_imports()
    
    # Test 3: Test PDF generation (only if reportlab available)
    pdf_generation_ok = False
    if reportlab_ok and imports_ok:
        pdf_generation_ok = test_pdf_generation_with_mock_data()
    
    # Test 4: Test server integration
    server_ok = test_server_integration()
    
    print("\n" + "=" * 40)
    print("TEST SUMMARY")
    print("=" * 40)
    
    checks = [
        ("reportlab Installation", reportlab_ok),
        ("PDF Imports", imports_ok),
        ("PDF Generation", pdf_generation_ok),
        ("Server Integration", server_ok),
    ]
    
    all_passed = True
    for check_name, passed in checks:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{check_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("\nPDF report generation is ready to use!")
        print("\nFeatures available:")
        print("• Professional PDF reports with tables and formatting")
        print("• Patient information integration")
        print("• Clinical interpretation with color coding")
        print("• Automatic timestamp and file naming")
        print("• Combined text and PDF report generation")
        
        print("\nUsage:")
        print("1. Ensure reportlab is installed: pip install reportlab")
        print("2. Use generate_pdf_report() for PDF only")
        print("3. Use generate_report_with_pdf() for both formats")
        print("4. Server automatically generates PDF when available")
    else:
        print("❌ SOME TESTS FAILED")
        if not reportlab_ok:
            print("\nTo enable PDF generation:")
            print("1. Install reportlab: pip install reportlab")
            print("2. Restart the application")
            print("3. PDF reports will be automatically available")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
