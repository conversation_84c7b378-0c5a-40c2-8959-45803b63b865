[2025-05-05 11:17:09,936] {C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py:875} ERROR - Exception on /upload [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Stroke\Stroke\Prediction_Scripts\flask-app\server.py", line 183, in upload_file
    coregister_to_template_space(file_path, template_path, temp_dir)
  File "D:\Stroke\Stroke\Prediction_Scripts\flask-app\server.py", line 38, in coregister_to_template_space
    image = ants.image_read(filename)
            ^^^^^^^^^^^^^^^
AttributeError: module 'ants' has no attribute 'image_read'
[2025-05-05 11:17:10,149] {C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py:875} ERROR - Exception on /upload [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Stroke\Stroke\Prediction_Scripts\flask-app\server.py", line 183, in upload_file
    coregister_to_template_space(file_path, template_path, temp_dir)
  File "D:\Stroke\Stroke\Prediction_Scripts\flask-app\server.py", line 38, in coregister_to_template_space
    image = ants.image_read(filename)
            ^^^^^^^^^^^^^^^
AttributeError: module 'ants' has no attribute 'image_read'
[2025-05-05 11:20:26,966] {C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py:875} ERROR - Exception on /upload [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Stroke\Stroke\Prediction_Scripts\flask-app\server.py", line 192, in upload_file
    result_file_path = predict_function(temp_dir, file.filename, modal)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Stroke\Stroke\Prediction_Scripts\flask-app\server.py", line 82, in predict_function
    predictor.predict_from_files([[os.path.join(temp_dir, filename)]],
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\nnunetv2\inference\predict_from_raw_data.py", line 270, in predict_from_files
    return self.predict_from_data_iterator(data_iterator, save_probabilities, num_processes_segmentation_export)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\nnunetv2\inference\predict_from_raw_data.py", line 363, in predict_from_data_iterator
    for preprocessed in data_iterator:
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\nnunetv2\inference\data_iterators.py", line 117, in preprocessing_iterator_fromfiles
    [i.pin_memory() for i in item.values() if isinstance(i, torch.Tensor)]
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\nnunetv2\inference\data_iterators.py", line 117, in <listcomp>
    [i.pin_memory() for i in item.values() if isinstance(i, torch.Tensor)]
     ^^^^^^^^^^^^^^
RuntimeError: Cannot access accelerator device when none is available.
[2025-05-05 11:21:35,390] {C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py:875} ERROR - Exception on /upload [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Stroke\Stroke\Prediction_Scripts\flask-app\server.py", line 192, in upload_file
    result_file_path = predict_function(temp_dir, file.filename, modal)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Stroke\Stroke\Prediction_Scripts\flask-app\server.py", line 82, in predict_function
    predictor.predict_from_files([[os.path.join(temp_dir, filename)]],
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\nnunetv2\inference\predict_from_raw_data.py", line 270, in predict_from_files
    return self.predict_from_data_iterator(data_iterator, save_probabilities, num_processes_segmentation_export)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\nnunetv2\inference\predict_from_raw_data.py", line 363, in predict_from_data_iterator
    for preprocessed in data_iterator:
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\nnunetv2\inference\data_iterators.py", line 117, in preprocessing_iterator_fromfiles
    [i.pin_memory() for i in item.values() if isinstance(i, torch.Tensor)]
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\nnunetv2\inference\data_iterators.py", line 117, in <listcomp>
    [i.pin_memory() for i in item.values() if isinstance(i, torch.Tensor)]
     ^^^^^^^^^^^^^^
RuntimeError: Cannot access accelerator device when none is available.
[2025-05-30 23:19:50,523] {C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py:875} ERROR - Exception on /upload [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Stroke\Stroke\Prediction_Scripts\flask-app\server.py", line 290, in upload_file
    zip_nii_gz_files(temp_dir, os.path.join(temp_dir, f'{str(file.filename)}.zip'),
  File "D:\Stroke\Stroke\Prediction_Scripts\flask-app\server.py", line 205, in zip_nii_gz_files
    f.write(region_info['report'])
            ~~~~~~~~~~~^^^^^^^^^^
KeyError: 'report'
