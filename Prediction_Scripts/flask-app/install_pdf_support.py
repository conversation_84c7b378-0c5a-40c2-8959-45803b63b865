"""
Installation script for PDF report generation support.
"""

import subprocess
import sys
import os

def install_reportlab():
    """Install reportlab package for PDF generation."""
    print("Installing reportlab for PDF report generation...")
    print("=" * 50)
    
    try:
        # Try to import reportlab first
        import reportlab
        print(f"✅ reportlab is already installed (version {reportlab.Version})")
        return True
    except ImportError:
        print("reportlab not found. Installing...")
        
        try:
            # Install reportlab using pip
            subprocess.check_call([sys.executable, "-m", "pip", "install", "reportlab"])
            print("✅ reportlab installed successfully!")
            
            # Verify installation
            import reportlab
            print(f"✅ Verification successful (version {reportlab.Version})")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install reportlab: {e}")
            return False
        except ImportError:
            print("❌ Installation completed but import failed")
            return False

def test_pdf_functionality():
    """Test basic PDF functionality."""
    print("\nTesting PDF functionality...")
    print("=" * 30)
    
    try:
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate, Paragraph
        from reportlab.lib.styles import getSampleStyleSheet
        
        # Create a simple test PDF
        test_file = "test_pdf_functionality.pdf"
        doc = SimpleDocTemplate(test_file, pagesize=letter)
        styles = getSampleStyleSheet()
        
        story = [
            Paragraph("PDF Generation Test", styles['Title']),
            Paragraph("This is a test PDF to verify functionality.", styles['Normal'])
        ]
        
        doc.build(story)
        
        if os.path.exists(test_file):
            file_size = os.path.getsize(test_file)
            print(f"✅ Test PDF created successfully ({file_size} bytes)")
            os.remove(test_file)  # Clean up
            print("✅ Test file cleaned up")
            return True
        else:
            print("❌ Test PDF was not created")
            return False
            
    except Exception as e:
        print(f"❌ PDF functionality test failed: {e}")
        return False

def update_system_status():
    """Update system to recognize PDF capabilities."""
    print("\nUpdating system status...")
    print("=" * 25)
    
    try:
        # Test import of our PDF functions
        sys.path.insert(0, os.getcwd())
        from brain_region_mapping import PDF_AVAILABLE, generate_pdf_report
        
        if PDF_AVAILABLE:
            print("✅ PDF generation is now available in brain_region_mapping")
            print("✅ generate_pdf_report function is accessible")
            
            # Test server integration
            from server import PDF_AVAILABLE as SERVER_PDF_AVAILABLE
            if SERVER_PDF_AVAILABLE:
                print("✅ PDF generation is available in server")
                return True
            else:
                print("❌ PDF generation not recognized in server")
                return False
        else:
            print("❌ PDF generation still not available")
            return False
            
    except Exception as e:
        print(f"❌ System status update failed: {e}")
        return False

def show_usage_instructions():
    """Show usage instructions for PDF generation."""
    print("\nPDF Report Generation Usage")
    print("=" * 30)
    
    print("1. Generate PDF report only:")
    print("   from brain_region_mapping import generate_pdf_report")
    print("   pdf_path = generate_pdf_report(analysis_result, 'report.pdf', patient_info)")
    
    print("\n2. Generate both text and PDF reports:")
    print("   from brain_region_mapping import generate_report_with_pdf")
    print("   reports = generate_report_with_pdf(analysis_result, output_dir, patient_info)")
    
    print("\n3. Server integration (automatic):")
    print("   The server will automatically generate PDF reports when available")
    print("   No code changes needed - PDF generation is transparent")
    
    print("\n4. Patient information format:")
    print("   patient_info = {")
    print("       'Patient ID': 'P001',")
    print("       'Age': '65',")
    print("       'Gender': 'Male',")
    print("       'Scan Date': '2024-01-15'")
    print("   }")
    
    print("\n5. PDF features:")
    print("   • Professional formatting with tables")
    print("   • Color-coded severity assessment")
    print("   • Clinical interpretation sections")
    print("   • Automatic timestamps")
    print("   • Patient information integration")

def main():
    """Main installation and setup process."""
    print("PDF Report Generation Setup")
    print("=" * 30)
    print("This script will install and configure PDF report generation for stroke analysis.")
    print()
    
    # Step 1: Install reportlab
    install_success = install_reportlab()
    
    if not install_success:
        print("\n❌ Installation failed. PDF generation will not be available.")
        print("You can try manual installation:")
        print("   pip install reportlab")
        return False
    
    # Step 2: Test PDF functionality
    test_success = test_pdf_functionality()
    
    if not test_success:
        print("\n❌ PDF functionality test failed.")
        return False
    
    # Step 3: Update system status
    status_success = update_system_status()
    
    if not status_success:
        print("\n❌ System status update failed.")
        print("You may need to restart the application.")
        return False
    
    # Step 4: Show usage instructions
    show_usage_instructions()
    
    print("\n" + "=" * 50)
    print("🎉 PDF REPORT GENERATION SETUP COMPLETE!")
    print("=" * 50)
    
    print("\nSetup Summary:")
    print("✅ reportlab installed and verified")
    print("✅ PDF functionality tested")
    print("✅ System integration confirmed")
    print("✅ Ready for use")
    
    print("\nNext steps:")
    print("1. Restart your stroke analysis server if it's running")
    print("2. PDF reports will be automatically generated alongside text reports")
    print("3. Look for .pdf files in your output directory")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Setup completed successfully!")
    else:
        print("\n❌ Setup failed. Please check the error messages above.")
    
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
