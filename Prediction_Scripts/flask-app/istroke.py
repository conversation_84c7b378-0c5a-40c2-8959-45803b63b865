import requests
import os
import argparse
import zipfile
import tempfile
import glob
from tqdm import tqdm
def upload_file(file_path, url, modal):
    with open(file_path, 'rb') as file:
        files = {'file': (os.path.basename(file_path), file)}
        data = {'modal': modal}
        response = requests.post(url, files=files, data=data)
        return response

def Upload_and_Predict(file_path, server_url, outputdir_path, modal):
    # 调用上传文件函数
    response = upload_file(file_path, server_url, modal)
    if response.status_code == 200:
        temp_dir = tempfile.mkdtemp()
        base_name = os.path.basename(file_path).split('.',1)[0]
        result_file_path = os.path.join(temp_dir,'predict_'+base_name+'.zip')

        with open(result_file_path, 'wb') as f:
            f.write(response.content)

        # Extract files from the zip
        with zipfile.ZipFile(result_file_path, 'r') as zip_ref:
            zip_ref.extractall(outputdir_path)

        # Check if stroke analysis information is available
        # First try to find an analysis file with the base name
        base_name = os.path.basename(file_path).split('.',1)[0]
        analysis_file_path = os.path.join(outputdir_path, f'stroke_analysis_{base_name}.txt')

        # If not found, try the default name
        if not os.path.exists(analysis_file_path):
            analysis_file_path = os.path.join(outputdir_path, 'stroke_analysis.txt')

        # Also check for old volume_info files for backward compatibility
        if not os.path.exists(analysis_file_path):
            volume_file_path = os.path.join(outputdir_path, f'volume_info_{base_name}.txt')
            if not os.path.exists(volume_file_path):
                volume_file_path = os.path.join(outputdir_path, 'volume_info.txt')
            if os.path.exists(volume_file_path):
                analysis_file_path = volume_file_path

        if os.path.exists(analysis_file_path):
            with open(analysis_file_path, 'r', encoding='utf-8') as f:
                analysis_info = f.read()
                print(analysis_info)
    else:
        print("Request failed, status code:", response.status_code)
    # 打印服务器返回的内容
    return response.content

def save_volume_summary(output_root, taskid):
    """
    Save a summary of all stroke analysis results in a single text file.

    :param output_root: Root directory containing all output subdirectories
    :param taskid: Task ID used to identify relevant subdirectories
    """
    summary_file_path = os.path.join(output_root, f'{taskid}_stroke_analysis_summary.txt')

    # Find all subdirectories with the taskid prefix
    task_dirs = [d for d in os.listdir(output_root) if os.path.isdir(os.path.join(output_root, d)) and d.startswith(taskid)]

    with open(summary_file_path, 'w', encoding='utf-8') as summary_file:
        summary_file.write("Stroke Analysis Summary\n")
        summary_file.write("=" * 50 + "\n\n")

        for task_dir in task_dirs:
            # Find all analysis files in the directory (new format)
            analysis_files = glob.glob(os.path.join(output_root, task_dir, 'stroke_analysis*.txt'))

            # Also check for old volume_info files for backward compatibility
            if not analysis_files:
                analysis_files = glob.glob(os.path.join(output_root, task_dir, 'volume_info*.txt'))

            for analysis_file_path in analysis_files:
                if os.path.exists(analysis_file_path):
                    with open(analysis_file_path, 'r', encoding='utf-8') as f:
                        analysis_info = f.read()
                        file_name = os.path.basename(analysis_file_path)
                        summary_file.write(f"Directory: {task_dir}\n")
                        summary_file.write(f"File: {file_name}\n")
                        summary_file.write("-" * 30 + "\n")
                        summary_file.write(analysis_info)
                        summary_file.write("\n" + "=" * 50 + "\n\n")

    print(f"Stroke analysis summary saved to: {summary_file_path}")
    return summary_file_path

def Process_in_Batch(root_dir, output_root, server_url, modal, taskid):
    for subdir, dirs, files in tqdm(os.walk(root_dir)):
        # 使用glob.glob来寻找当前遍历的文件夹中所有的.nii.gz文件
        for file in glob.glob(subdir + '/*.nii.gz') + glob.glob(subdir + '/*.nii'):
            subdir_name = taskid+'_'+os.path.basename(subdir)
            sub_result = os.path.join(output_root, subdir_name)
            Upload_and_Predict(file, server_url, sub_result, modal)

    # After processing all files, create a summary of volumes
    save_volume_summary(output_root, taskid)

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='manual to this script')
    parser.add_argument('--WorkingDir', type=str, default=r'D:/Stroke/Stroke/Input_Files')
    parser.add_argument('--subjects', type=str, default='ATLAS_r001s001 ATLAS_r001s002 ATLAS_r001s003 ATLAS_r001s004 ATLAS_r001s005')
    parser.add_argument('--taskid', type=str, default='Task001')
    parser.add_argument('--server_url', type=str, default='http://127.0.0.1:5000/upload')
    parser.add_argument('--modal', type=str, default='T1')
    args = parser.parse_args()
    WorkingDir = args.WorkingDir
    subjects = args.subjects
    taskid = args.taskid
    output_path = r"D:/Stroke/Stroke/Output_Files"

    substrings = subjects.split(' ')
    result_list = list(substrings)
    subject_list = [item for item in result_list]
    for sub in subject_list:
        if not os.path.exists(os.path.join(WorkingDir, sub)):
            print('The subject folder does not exist:', sub)
            continue
        else:
            print('Processing subject:', sub)
            input_path = os.path.join(WorkingDir, sub)
            Process_in_Batch(input_path, output_path, args.server_url, args.modal, taskid)
