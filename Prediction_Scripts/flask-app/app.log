[2025-05-31 00:17:23,689] {C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py:875} ERROR - Exception on /upload [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\nnunet1\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Stroke\Stroke\Prediction_Scripts\flask-app\server.py", line 284, in upload_file
    zip_nii_gz_files(temp_dir, os.path.join(temp_dir, f'{str(file.filename)}.zip'),
  File "D:\Stroke\Stroke\Prediction_Scripts\flask-app\server.py", line 199, in zip_nii_gz_files
    f.write(region_info['report'])
            ~~~~~~~~~~~^^^^^^^^^^
KeyError: 'report'
