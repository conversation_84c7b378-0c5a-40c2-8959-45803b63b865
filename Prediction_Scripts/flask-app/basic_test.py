"""
Basic test without heavy dependencies.
"""

print("Testing basic functionality...")

# Test 1: Import basic data structures
try:
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    
    from brain_region_mapping import AAL_REGIONS, REGION_GROUPS
    print("✓ Successfully imported AAL_REGIONS and REGION_GROUPS")
    print(f"  - AAL_REGIONS contains {len(AAL_REGIONS)} regions")
    print(f"  - REGION_GROUPS contains {len(REGION_GROUPS)} groups")
except Exception as e:
    print(f"✗ Import failed: {e}")
    exit(1)

# Test 2: Check data structure integrity
try:
    # Check if all region IDs in groups exist in AAL_REGIONS
    missing_regions = []
    for group_name, region_ids in REGION_GROUPS.items():
        for region_id in region_ids:
            if region_id not in AAL_REGIONS:
                missing_regions.append((group_name, region_id))
    
    if missing_regions:
        print(f"✗ Found missing regions: {missing_regions}")
    else:
        print("✓ All region group mappings are valid")
        
except Exception as e:
    print(f"✗ Data structure check failed: {e}")

# Test 3: Display sample data
print("\nSample AAL Regions:")
for i, (region_id, region_name) in enumerate(list(AAL_REGIONS.items())[:10]):
    print(f"  {region_id:2d}: {region_name}")

print("\nRegion Groups:")
for group_name, region_ids in REGION_GROUPS.items():
    print(f"  {group_name}: {len(region_ids)} regions")

print("\n✓ Basic tests completed successfully!")
print("\nNext steps:")
print("1. The brain region mapping module is ready")
print("2. You can now test the full server functionality")
print("3. Upload a brain image to see the region analysis in action")
