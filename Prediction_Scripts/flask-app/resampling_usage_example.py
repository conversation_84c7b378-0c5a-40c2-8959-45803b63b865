# Example of how the fixed system now works:

from brain_region_mapping import analyze_lesion_regions

# The system will now automatically handle dimension mismatches
result = analyze_lesion_regions(
    lesion_mask_path="path/to/lesion.nii",
    atlas_path="path/to/atlas.nii.gz"
)

# Expected output:
# Info: Lesion dimensions: (193, 229, 193), Atlas dimensions: (181, 217, 181)
# Resampling atlas to match lesion dimensions...
# Using scipy-based resampling for atlas...
# Resampling atlas from (181, 217, 181) to (193, 229, 193)
# Zoom factors: [1.066, 1.055, 1.066]
# Resampling completed successfully. Final shape: (193, 229, 193)
# Found X unique regions in atlas
# Analysis completed successfully!