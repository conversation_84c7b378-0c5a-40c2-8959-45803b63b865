"""
Simple test to check if the brain region mapping module can be imported.
"""

try:
    print("Testing brain region mapping import...")
    from brain_region_mapping import AAL_REGIONS, REGION_GROUPS
    print("✓ Successfully imported brain_region_mapping")
    
    print(f"✓ Found {len(AAL_REGIONS)} AAL regions")
    print(f"✓ Found {len(REGION_GROUPS)} region groups")
    
    # Test some basic functionality
    print("\nTesting region groups:")
    for group_name, region_ids in REGION_GROUPS.items():
        print(f"  {group_name}: {len(region_ids)} regions")
    
    print("\nSample AAL regions:")
    for i, (region_id, region_name) in enumerate(list(AAL_REGIONS.items())[:5]):
        print(f"  {region_id}: {region_name}")
    
    print("\n✓ All basic tests passed!")
    
except ImportError as e:
    print(f"✗ Import error: {e}")
except Exception as e:
    print(f"✗ Error: {e}")
