"""
Verification script for AAL3 updates - no heavy dependencies.
"""

import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_aal3_regions():
    """Verify AAL3 regions are properly defined."""
    try:
        # Import without numpy dependencies
        import importlib.util
        spec = importlib.util.spec_from_file_location("brain_region_mapping", 
                                                     "brain_region_mapping.py")
        module = importlib.util.module_from_spec(spec)
        
        # Get the dictionaries directly from the file
        with open("brain_region_mapping.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check if AAL_REGIONS is properly updated
        if "AAL3 Atlas Region Labels - Complete list from AAL3v1.nii.txt" in content:
            print("✅ AAL_REGIONS updated with AAL3 complete list")
        else:
            print("❌ AAL_REGIONS not properly updated")
            return False
        
        # Check for key regions
        key_checks = [
            ("15: \"Supp_Motor_Area_L\"", "Left Supplementary Motor Area"),
            ("16: \"Supp_Motor_Area_R\"", "Right Supplementary Motor Area"),
            ("63: \"Parietal_Sup_L\"", "Left Superior Parietal"),
            ("64: \"Parietal_Sup_R\"", "Right Superior Parietal"),
            ("65: \"Parietal_Inf_L\"", "Left Inferior Parietal"),
            ("66: \"Parietal_Inf_R\"", "Right Inferior Parietal"),
            ("67: \"SupraMarginal_L\"", "Left SupraMarginal"),
            ("68: \"SupraMarginal_R\"", "Right SupraMarginal"),
            ("69: \"Angular_L\"", "Left Angular"),
            ("70: \"Angular_R\"", "Right Angular"),
        ]
        
        for region_def, description in key_checks:
            if region_def in content:
                print(f"✅ {description} found")
            else:
                print(f"❌ {description} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking AAL_REGIONS: {e}")
        return False

def verify_region_groups():
    """Verify region groups are properly updated."""
    try:
        with open("brain_region_mapping.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check for enhanced region groups
        if "Enhanced region groupings for clinical interpretation based on AAL3" in content:
            print("✅ REGION_GROUPS updated with enhanced groupings")
        else:
            print("❌ REGION_GROUPS not properly updated")
            return False
        
        # Check for key groups
        key_groups = [
            ("\"Supplementary Motor Area\": [15, 16]", "Dedicated SMA group"),
            ("\"Parietal Lobe - Superior\": [63, 64]", "Superior parietal subdivision"),
            ("\"Parietal Lobe - Inferior\": [65, 66]", "Inferior parietal subdivision"),
            ("\"Parietal Lobe - SupraMarginal\": [67, 68]", "SupraMarginal subdivision"),
            ("\"Parietal Lobe - Angular\": [69, 70]", "Angular subdivision"),
        ]
        
        for group_def, description in key_groups:
            if group_def in content:
                print(f"✅ {description} found")
            else:
                print(f"❌ {description} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking REGION_GROUPS: {e}")
        return False

def verify_analysis_function():
    """Verify the analysis function is updated."""
    try:
        with open("brain_region_mapping.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check for AAL3 integration
        checks = [
            ("Look for AAL3v1.nii.gz in the flask-app directory", "AAL3 auto-detection"),
            ("atlas_path = os.path.join(script_dir, 'AAL3v1.nii.gz')", "AAL3 path setup"),
            ("'atlas_used': atlas_path", "Atlas tracking in results"),
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"✅ {description} implemented")
            else:
                print(f"❌ {description} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking analysis function: {e}")
        return False

def verify_report_function():
    """Verify the report function is enhanced."""
    try:
        with open("brain_region_mapping.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check for enhanced reporting
        checks = [
            ("STROKE LESION - BRAIN REGION ANALYSIS REPORT", "Enhanced report header"),
            ("KEY CLINICAL FINDINGS:", "Clinical findings section"),
            ("Supplementary Motor Area", "SMA specific reporting"),
            ("Parietal lobe involvement may affect", "Parietal clinical interpretation"),
            ("CLINICAL INTERPRETATION NOTES:", "Clinical notes section"),
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"✅ {description} implemented")
            else:
                print(f"❌ {description} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking report function: {e}")
        return False

def verify_server_updates():
    """Verify server.py is updated."""
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check for server updates
        checks = [
            ("aal_atlas_path = os.path.join(script_dir, 'AAL3v1.nii.gz')", "Server AAL3 path"),
            ("atlas_path=aal_atlas_path,  # Use the real AAL3 atlas", "Server AAL3 usage"),
            ("using AAL3 atlas", "Server documentation update"),
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"✅ {description} implemented")
            else:
                print(f"❌ {description} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking server updates: {e}")
        return False

def verify_files_exist():
    """Verify required files exist."""
    required_files = [
        ("AAL3v1.nii.gz", "AAL3 atlas file"),
        ("AAL3v1.nii.txt", "AAL3 labels file"),
        ("brain_region_mapping.py", "Updated mapping module"),
        ("server.py", "Updated server"),
    ]
    
    all_exist = True
    for filename, description in required_files:
        if os.path.exists(filename):
            if filename.endswith('.gz'):
                size_mb = os.path.getsize(filename) / (1024 * 1024)
                print(f"✅ {description} exists ({size_mb:.1f} MB)")
            else:
                print(f"✅ {description} exists")
        else:
            print(f"❌ {description} missing: {filename}")
            all_exist = False
    
    return all_exist

def main():
    """Run all verification checks."""
    print("AAL3 Integration Verification")
    print("=" * 40)
    
    print("\n1. Checking AAL_REGIONS dictionary...")
    regions_ok = verify_aal3_regions()
    
    print("\n2. Checking REGION_GROUPS dictionary...")
    groups_ok = verify_region_groups()
    
    print("\n3. Checking analysis function...")
    analysis_ok = verify_analysis_function()
    
    print("\n4. Checking report function...")
    report_ok = verify_report_function()
    
    print("\n5. Checking server updates...")
    server_ok = verify_server_updates()
    
    print("\n6. Checking required files...")
    files_ok = verify_files_exist()
    
    print("\n" + "=" * 40)
    print("VERIFICATION SUMMARY")
    print("=" * 40)
    
    checks = [
        ("AAL_REGIONS Dictionary", regions_ok),
        ("REGION_GROUPS Dictionary", groups_ok),
        ("Analysis Function", analysis_ok),
        ("Report Function", report_ok),
        ("Server Updates", server_ok),
        ("Required Files", files_ok),
    ]
    
    all_passed = True
    for check_name, passed in checks:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{check_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 ALL CHECKS PASSED!")
        print("\nYour AAL3 integration is complete and ready to use!")
        print("\nKey improvements:")
        print("• Complete AAL3 atlas with 170 brain regions")
        print("• Enhanced parietal lobe analysis (4 subdivisions)")
        print("• Dedicated supplementary motor area tracking")
        print("• Comprehensive clinical interpretation")
        print("• Detailed volume and percentage reporting")
        print("\nYou can now use the enhanced brain region analysis!")
    else:
        print("❌ SOME CHECKS FAILED")
        print("Please review the failed checks above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
