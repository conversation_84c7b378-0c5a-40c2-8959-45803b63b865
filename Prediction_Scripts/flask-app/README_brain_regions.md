# 脑区分析功能说明

## 概述

本项目已增加了脑区分析功能，能够识别中风病灶覆盖的脑区信息，包括：
- 运动区、感觉区等功能区域
- 额叶、顶叶、颞叶、枕叶等解剖区域
- AAL图谱分区（如左侧辅助运动区等）

## 新增功能

### 1. 脑区映射分析
- 自动识别病灶覆盖的大脑区域
- 计算每个受影响区域的体积和覆盖百分比
- 按解剖系统分组显示结果

### 2. 详细报告生成
- 生成包含脑区信息的详细分析报告
- 报告包括体积信息和脑区分析
- 支持中英文显示

### 3. 批量处理支持
- 支持批量处理多个病例
- 生成汇总报告

## 文件结构

```
Prediction_Scripts/flask-app/
├── brain_region_mapping.py     # 脑区映射核心功能
├── server.py                   # 更新的服务器端代码
├── istroke.py                  # 更新的客户端代码
├── test_brain_regions.py       # 测试脚本
└── README_brain_regions.md     # 本说明文件
```

## 使用方法

### 1. 单个文件处理

使用现有的Flask服务器接口，上传脑部图像文件：

```python
# 启动服务器
python server.py

# 使用客户端上传文件
python istroke.py --WorkingDir "输入目录" --subjects "病例名称"
```

### 2. 测试功能

运行测试脚本验证功能：

```bash
cd Prediction_Scripts/flask-app
python test_brain_regions.py
```

### 3. 输出文件

处理完成后，输出目录将包含：
- `mask_*.nii.gz`: 病灶分割结果
- `stroke_analysis_*.txt`: 详细分析报告（包含体积和脑区信息）
- `*_stroke_analysis_summary.txt`: 批量处理汇总报告

## 报告示例

```
File: patient001.nii.gz
==================================================

Stroke Region Volume: 1234.56 cubic mm

BRAIN REGION ANALYSIS
==================================================

Brain Region Analysis Report
========================================

Total Lesion Volume: 1234.56 cubic mm

Affected Anatomical Systems:
------------------------------
Motor Areas: 456.78 mm³ (37.0% of lesion)
  - Precentral_L
  - Supp_Motor_Area_L

Frontal Lobe: 234.56 mm³ (19.0% of lesion)
  - Frontal_Mid_L
  - Frontal_Inf_Oper_L

Detailed Region Analysis:
-------------------------
Precentral_L:
  Volume affected: 345.67 mm³
  Percentage of region: 12.3%

Supp_Motor_Area_L:
  Volume affected: 111.11 mm³
  Percentage of region: 8.9%
```

## 技术实现

### 1. 脑区图谱
- 基于AAL（Automated Anatomical Labeling）图谱
- 包含主要的大脑皮层和皮层下结构
- 支持左右半球分别标注

### 2. 分析算法
- 将病灶mask与脑区图谱进行重叠分析
- 计算每个脑区的受影响体积
- 按解剖系统分组统计

### 3. 坐标空间
- 所有分析在MNI152标准空间进行
- 确保结果的一致性和可比性

## 注意事项

1. **图谱精度**: 当前使用简化的AAL图谱，如需更高精度，建议下载完整的AAL3图谱
2. **配准质量**: 脑区分析的准确性依赖于图像配准到MNI152空间的质量
3. **计算资源**: 脑区分析会增加一定的计算时间，但通常在几秒内完成

## 扩展功能

### 1. 添加完整AAL图谱
如需使用完整的AAL3图谱，请：
1. 下载AAL3.nii.gz文件
2. 将文件放置在flask-app目录下
3. 修改`brain_region_mapping.py`中的图谱路径

### 2. 自定义脑区分组
可以在`brain_region_mapping.py`中的`REGION_GROUPS`字典中添加自定义的脑区分组。

### 3. 多语言支持
可以在`AAL_REGIONS`字典中添加中文区域名称，实现中英文对照显示。

## 故障排除

### 1. 导入错误
如果出现模块导入错误，请确保：
- `brain_region_mapping.py`文件在正确位置
- Python路径设置正确

### 2. 内存不足
如果处理大文件时出现内存不足：
- 检查可用内存
- 考虑使用更小的图像分辨率

### 3. 配准失败
如果图像配准失败：
- 检查输入图像质量
- 确认MNI152模板文件完整

## 联系支持

如有问题或建议，请联系开发团队。
