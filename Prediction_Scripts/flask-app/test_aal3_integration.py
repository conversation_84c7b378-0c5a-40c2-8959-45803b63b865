"""
Test script for the updated AAL3 brain region mapping functionality.
This script tests the enhanced brain region analysis with the real AAL3 atlas.
"""

import os
import sys
import numpy as np
import nibabel as nib
from brain_region_mapping import analyze_lesion_regions, generate_region_report, AAL_REGIONS, REGION_GROUPS

def test_aal3_atlas_loading():
    """
    Test if the AAL3 atlas can be loaded properly.
    """
    print("Testing AAL3 Atlas Loading...")
    print("=" * 40)
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    aal_atlas_path = os.path.join(script_dir, 'AAL3v1.nii.gz')
    
    if not os.path.exists(aal_atlas_path):
        print(f"❌ ERROR: AAL3 atlas not found at {aal_atlas_path}")
        return False
    
    try:
        # Load the atlas
        atlas_img = nib.load(aal_atlas_path)
        atlas_data = atlas_img.get_fdata()
        
        # Get unique labels
        unique_labels = np.unique(atlas_data[atlas_data > 0])
        
        print(f"✅ AAL3 atlas loaded successfully!")
        print(f"   Atlas dimensions: {atlas_data.shape}")
        print(f"   Number of unique regions: {len(unique_labels)}")
        print(f"   Label range: {int(unique_labels.min())} - {int(unique_labels.max())}")
        
        # Check how many labels we have definitions for
        defined_labels = [label for label in unique_labels if int(label) in AAL_REGIONS]
        print(f"   Regions with definitions: {len(defined_labels)}/{len(unique_labels)}")
        
        # Show some example regions
        print(f"   Example regions:")
        for i, label in enumerate(unique_labels[:5]):
            label_int = int(label)
            region_name = AAL_REGIONS.get(label_int, f"Unknown_{label_int}")
            print(f"     Label {label_int}: {region_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR loading AAL3 atlas: {str(e)}")
        return False

def test_region_groups():
    """
    Test the region groupings.
    """
    print("\nTesting Region Groups...")
    print("=" * 30)
    
    print(f"Number of region groups defined: {len(REGION_GROUPS)}")
    
    # Check key groups
    key_groups = ["Motor Areas", "Supplementary Motor Area", "Parietal Lobe", 
                  "Parietal Lobe - Superior", "Parietal Lobe - Inferior"]
    
    for group in key_groups:
        if group in REGION_GROUPS:
            regions = REGION_GROUPS[group]
            print(f"✅ {group}: {len(regions)} regions")
            # Show region names
            region_names = [AAL_REGIONS.get(r, f"Unknown_{r}") for r in regions]
            print(f"   Regions: {', '.join(region_names)}")
        else:
            print(f"❌ {group}: Not found")
    
    return True

def create_test_lesion_mask_aal3(template_path, output_path):
    """
    Create a test lesion mask that overlaps with known AAL3 regions.
    """
    # Load template to get dimensions and affine
    template_img = nib.load(template_path)
    template_data = template_img.get_fdata()
    
    # Create a lesion mask
    lesion_data = np.zeros_like(template_data)
    
    # Create multiple small lesions in different areas
    # These coordinates are approximate for MNI152 space
    
    # Left motor area lesion
    lesion_data[45:55, 25:35, 50:60] = 1
    
    # Left parietal lesion  
    lesion_data[65:75, 25:35, 45:55] = 1
    
    # Small frontal lesion
    lesion_data[35:40, 30:35, 55:60] = 1
    
    # Save the lesion mask
    lesion_img = nib.Nifti1Image(lesion_data, template_img.affine, template_img.header)
    nib.save(lesion_img, output_path)
    
    return output_path

def test_full_analysis_pipeline():
    """
    Test the complete analysis pipeline with AAL3.
    """
    print("\nTesting Full Analysis Pipeline...")
    print("=" * 40)
    
    # Paths
    template_path = r"D:/Stroke/Stroke/Prediction_Scripts/flask-app/MNI152_T1_1mm.nii"
    test_lesion_path = "test_lesion_aal3.nii"
    
    # Check if template exists
    if not os.path.exists(template_path):
        print(f"❌ ERROR: Template file not found at {template_path}")
        return False
    
    try:
        # Create a test lesion mask
        print("Creating test lesion mask...")
        create_test_lesion_mask_aal3(template_path, test_lesion_path)
        print(f"✅ Test lesion mask created: {test_lesion_path}")
        
        # Test the region analysis with AAL3
        print("\nRunning brain region analysis with AAL3...")
        region_analysis = analyze_lesion_regions(
            lesion_mask_path=test_lesion_path,
            atlas_path=None,  # Will automatically find AAL3v1.nii.gz
            template_path=template_path
        )
        
        if 'error' in region_analysis:
            print(f"❌ ERROR in analysis: {region_analysis['error']}")
            return False
        
        # Generate report
        print("Generating comprehensive report...")
        report = generate_region_report(region_analysis)
        
        # Display results
        print("\n" + "=" * 60)
        print("BRAIN REGION ANALYSIS RESULTS (AAL3)")
        print("=" * 60)
        print(report)
        
        # Save results to file
        with open("test_aal3_analysis_results.txt", "w", encoding="utf-8") as f:
            f.write("Test AAL3 Brain Region Analysis Results\n")
            f.write("=" * 60 + "\n\n")
            f.write(report)
        
        print(f"\n✅ Results saved to: test_aal3_analysis_results.txt")
        
        # Summary statistics
        affected_regions = region_analysis.get('affected_regions', {})
        affected_groups = region_analysis.get('affected_groups', {})
        
        print(f"\nSUMMARY STATISTICS:")
        print(f"- Total regions affected: {len(affected_regions)}")
        print(f"- Anatomical groups affected: {len(affected_groups)}")
        print(f"- Total lesion volume: {region_analysis.get('total_lesion_volume_mm3', 0):.2f} mm³")
        
        # Clean up test files
        if os.path.exists(test_lesion_path):
            os.remove(test_lesion_path)
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR during testing: {str(e)}")
        return False

def main():
    """
    Run all tests for the AAL3 integration.
    """
    print("AAL3 Brain Region Mapping Integration Test Suite")
    print("=" * 60)
    
    # Test 1: AAL3 atlas loading
    atlas_success = test_aal3_atlas_loading()
    
    # Test 2: Region groups
    groups_success = test_region_groups()
    
    # Test 3: Full analysis pipeline
    analysis_success = test_full_analysis_pipeline()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"AAL3 Atlas Loading: {'✅ PASSED' if atlas_success else '❌ FAILED'}")
    print(f"Region Groups: {'✅ PASSED' if groups_success else '❌ FAILED'}")
    print(f"Full Analysis Pipeline: {'✅ PASSED' if analysis_success else '❌ FAILED'}")
    
    if atlas_success and groups_success and analysis_success:
        print("\n🎉 All tests PASSED! AAL3 integration is working correctly.")
        print("\nKey improvements:")
        print("- ✅ Complete AAL3 atlas with 170 brain regions")
        print("- ✅ Enhanced parietal lobe subdivision analysis")
        print("- ✅ Dedicated supplementary motor area tracking")
        print("- ✅ Comprehensive clinical interpretation")
        print("- ✅ Detailed volume and percentage reporting")
    else:
        print("\n❌ Some tests FAILED. Please check the error messages above.")
        
    return atlas_success and groups_success and analysis_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
