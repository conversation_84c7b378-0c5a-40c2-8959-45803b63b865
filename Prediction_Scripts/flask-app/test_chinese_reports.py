"""
测试中文报告生成功能
"""

import os
import sys

def test_chinese_report_functions():
    """测试中文报告生成函数"""
    print("测试中文报告生成功能...")
    print("=" * 30)
    
    try:
        # 导入中文报告函数
        from brain_region_mapping import generate_chinese_report, generate_bilingual_reports
        
        print("✅ 中文报告函数导入成功")
        
        # 创建模拟分析数据
        mock_analysis = {
            'total_lesion_volume_mm3': 1234.56,
            'atlas_used': 'AAL3v1_1mm.nii.gz',
            'affected_regions': {
                15: {
                    'name': 'Supp_Motor_Area_L',
                    'overlap_volume_mm3': 156.78,
                    'region_total_volume_mm3': 500.0,
                    'overlap_percentage': 31.4
                },
                57: {
                    'name': 'Parietal_Sup_L',
                    'overlap_volume_mm3': 234.56,
                    'region_total_volume_mm3': 800.0,
                    'overlap_percentage': 29.3
                },
                1: {
                    'name': 'Precentral_L',
                    'overlap_volume_mm3': 345.67,
                    'region_total_volume_mm3': 1200.0,
                    'overlap_percentage': 28.8
                }
            },
            'affected_groups': {
                'Motor Areas': {
                    'total_volume_mm3': 502.45,
                    'affected_regions': ['Precentral_L', 'Supp_Motor_Area_L']
                },
                'Supplementary Motor Area': {
                    'total_volume_mm3': 156.78,
                    'affected_regions': ['Supp_Motor_Area_L']
                },
                'Parietal Lobe - Superior': {
                    'total_volume_mm3': 234.56,
                    'affected_regions': ['Parietal_Sup_L']
                }
            }
        }
        
        # 测试中文报告生成
        print("\n生成中文报告...")
        chinese_report = generate_chinese_report(mock_analysis)
        
        if chinese_report and "中风病灶 - 脑区分析报告" in chinese_report:
            print("✅ 中文报告生成成功")
            
            # 保存中文报告示例
            with open("test_chinese_report.txt", "w", encoding="utf-8") as f:
                f.write(chinese_report)
            print("✅ 中文报告已保存到 test_chinese_report.txt")
            
            # 显示报告片段
            lines = chinese_report.split('\n')
            print("\n中文报告预览:")
            print("-" * 20)
            for line in lines[:15]:  # 显示前15行
                print(line)
            print("...")
            
        else:
            print("❌ 中文报告生成失败")
            return False
        
        # 测试双语报告生成
        print("\n生成双语报告...")
        bilingual_reports = generate_bilingual_reports(mock_analysis, ".")
        
        if (bilingual_reports['english_report_path'] and 
            bilingual_reports['chinese_report_path'] and
            os.path.exists(bilingual_reports['english_report_path']) and
            os.path.exists(bilingual_reports['chinese_report_path'])):
            
            print("✅ 双语报告生成成功")
            print(f"   英文报告: {os.path.basename(bilingual_reports['english_report_path'])}")
            print(f"   中文报告: {os.path.basename(bilingual_reports['chinese_report_path'])}")
            
            # 清理测试文件
            for path in [bilingual_reports['english_report_path'], 
                        bilingual_reports['chinese_report_path'],
                        "test_chinese_report.txt"]:
                if os.path.exists(path):
                    os.remove(path)
                    print(f"   清理: {os.path.basename(path)}")
            
            return True
        else:
            print("❌ 双语报告生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_server_integration():
    """测试服务器集成"""
    print("\n测试服务器集成...")
    print("=" * 20)
    
    try:
        from server import analyze_stroke_regions
        
        # 检查函数签名
        import inspect
        sig = inspect.signature(analyze_stroke_regions)
        params = list(sig.parameters.keys())
        
        if 'generate_chinese' in params:
            print("✅ 服务器函数已更新支持中文报告")
            print(f"   参数: {params}")
            return True
        else:
            print("❌ 服务器函数未更新")
            print(f"   当前参数: {params}")
            return False
            
    except Exception as e:
        print(f"❌ 服务器集成测试失败: {e}")
        return False

def test_chinese_content():
    """测试中文内容的正确性"""
    print("\n测试中文内容...")
    print("=" * 15)
    
    try:
        from brain_region_mapping import generate_chinese_report
        
        # 简单的测试数据
        test_analysis = {
            'total_lesion_volume_mm3': 1000.0,
            'atlas_used': 'AAL3v1_1mm.nii.gz',
            'affected_regions': {
                15: {
                    'name': 'Supp_Motor_Area_L',
                    'overlap_volume_mm3': 100.0,
                    'region_total_volume_mm3': 400.0,
                    'overlap_percentage': 25.0
                }
            },
            'affected_groups': {
                'Supplementary Motor Area': {
                    'total_volume_mm3': 100.0,
                    'affected_regions': ['Supp_Motor_Area_L']
                }
            }
        }
        
        chinese_report = generate_chinese_report(test_analysis)
        
        # 检查关键中文内容
        chinese_keywords = [
            "中风病灶 - 脑区分析报告",
            "病灶总体积",
            "立方毫米",
            "毫升",
            "病灶大小分类",
            "重点临床发现",
            "辅助运动区",
            "临床解释说明",
            "重要提示"
        ]
        
        missing_keywords = []
        for keyword in chinese_keywords:
            if keyword not in chinese_report:
                missing_keywords.append(keyword)
        
        if not missing_keywords:
            print("✅ 所有中文关键词都存在")
            return True
        else:
            print(f"❌ 缺少中文关键词: {missing_keywords}")
            return False
            
    except Exception as e:
        print(f"❌ 中文内容测试失败: {e}")
        return False

def show_chinese_report_example():
    """显示中文报告示例"""
    print("\n中文报告示例...")
    print("=" * 15)
    
    example_report = """
中风病灶 - 脑区分析报告
==================================================

使用图谱: AAL3v1_1mm.nii.gz
病灶总体积: 1234.56 立方毫米 (1.23 毫升)
病灶大小分类: 中型

重点临床发现:
-------------------------
• 辅助运动区: 156.78 立方毫米 (占病灶 12.7%)
  - Supp_Motor_Area_L
• 顶叶 - 上部: 234.56 立方毫米 (占病灶 19.0%)
  - Parietal_Sup_L

所有受累解剖系统:
-----------------------------------
运动区: 502.45 立方毫米 (占病灶 40.7%)
  - Precentral_L
  - Supp_Motor_Area_L

临床解释说明:
------------------------------
• 由于运动区受累，运动功能可能受损
• 辅助运动区受累可能影响运动规划和协调功能
• 顶叶受累可能影响空间处理、注意力和感觉整合功能

重要提示:
----------
本分析基于病灶与标准脑图谱的解剖重叠情况。
结果应结合临床评估和功能检查进行综合解释。
"""
    
    print(example_report)
    return True

def main():
    """运行所有中文报告测试"""
    print("中文报告生成功能测试套件")
    print("=" * 30)
    
    # 测试1: 中文报告函数
    functions_ok = test_chinese_report_functions()
    
    # 测试2: 服务器集成
    server_ok = test_server_integration()
    
    # 测试3: 中文内容
    content_ok = test_chinese_content()
    
    # 测试4: 显示示例
    example_ok = show_chinese_report_example()
    
    print("\n" + "=" * 30)
    print("测试总结")
    print("=" * 30)
    
    checks = [
        ("中文报告函数", functions_ok),
        ("服务器集成", server_ok),
        ("中文内容", content_ok),
        ("示例显示", example_ok),
    ]
    
    all_passed = True
    for check_name, passed in checks:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{check_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 30)
    if all_passed:
        print("🎉 所有测试通过!")
        print("\n中文报告功能已就绪!")
        print("\n功能特性:")
        print("• 完整的中文脑区分析报告")
        print("• 中文区域名称和临床解释")
        print("• 双语报告自动生成")
        print("• 服务器透明集成")
        print("• UTF-8编码支持")
        
        print("\n使用方法:")
        print("1. 服务器自动生成中英文双语报告")
        print("2. 手动调用: generate_chinese_report(analysis)")
        print("3. 双语报告: generate_bilingual_reports(analysis)")
    else:
        print("❌ 部分测试失败")
        print("请检查上述错误信息")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
