"""
Test script for the resampling functionality to resolve dimension mismatch.
"""

import os
import sys
import numpy as np
import nibabel as nib
from brain_region_mapping import analyze_lesion_regions, generate_region_report

def create_test_lesion_mask(template_path, output_path):
    """
    Create a test lesion mask using the template dimensions.
    """
    # Load template to get dimensions and affine
    template_img = nib.load(template_path)
    template_data = template_img.get_fdata()
    
    print(f"Template dimensions: {template_data.shape}")
    print(f"Template voxel size: {template_img.header.get_zooms()}")
    
    # Create a lesion mask
    lesion_data = np.zeros_like(template_data)
    
    # Create multiple small lesions in different areas
    # These coordinates are for the template space
    
    # Central lesion (motor/parietal area)
    x_center, y_center, z_center = template_data.shape[0]//2, template_data.shape[1]//2, template_data.shape[2]//2
    
    # Motor area lesion
    lesion_data[x_center-10:x_center+10, y_center-20:y_center, z_center-5:z_center+5] = 1
    
    # Parietal lesion
    lesion_data[x_center+20:x_center+30, y_center-10:y_center+10, z_center-5:z_center+5] = 1
    
    # Small frontal lesion
    lesion_data[x_center-30:x_center-20, y_center-5:y_center+5, z_center:z_center+10] = 1
    
    # Save the lesion mask
    lesion_img = nib.Nifti1Image(lesion_data, template_img.affine, template_img.header)
    nib.save(lesion_img, output_path)
    
    # Calculate lesion volume
    voxel_dims = template_img.header.get_zooms()
    voxel_volume = voxel_dims[0] * voxel_dims[1] * voxel_dims[2]
    lesion_volume = np.sum(lesion_data > 0) * voxel_volume
    print(f"Created test lesion with volume: {lesion_volume:.2f} mm³")
    
    return output_path

def test_dimension_mismatch_resolution():
    """
    Test that the resampling functionality resolves dimension mismatches.
    """
    print("Testing Dimension Mismatch Resolution...")
    print("=" * 45)
    
    # Paths
    template_path = r"D:/Stroke/Stroke/Prediction_Scripts/flask-app/MNI152_T1_1mm.nii"
    atlas_path = "AAL3v1_1mm.nii.gz"
    test_lesion_path = "test_lesion_resampling.nii"
    
    # Check if files exist
    if not os.path.exists(template_path):
        print(f"❌ Template not found: {template_path}")
        return False
    
    if not os.path.exists(atlas_path):
        print(f"❌ Atlas not found: {atlas_path}")
        return False
    
    try:
        # Check original dimensions
        template_img = nib.load(template_path)
        atlas_img = nib.load(atlas_path)
        
        template_shape = template_img.get_fdata().shape
        atlas_shape = atlas_img.get_fdata().shape
        
        print(f"Template dimensions: {template_shape}")
        print(f"Atlas dimensions: {atlas_shape}")
        
        if template_shape != atlas_shape:
            print(f"✅ Dimension mismatch detected (as expected)")
            print(f"   Template: {template_shape}")
            print(f"   Atlas: {atlas_shape}")
        else:
            print(f"ℹ️  Dimensions already match")
        
        # Create test lesion
        print(f"\nCreating test lesion mask...")
        create_test_lesion_mask(template_path, test_lesion_path)
        
        # Test the analysis with resampling
        print(f"\nRunning analysis with automatic resampling...")
        region_analysis = analyze_lesion_regions(
            lesion_mask_path=test_lesion_path,
            atlas_path=atlas_path,
            template_path=template_path
        )
        
        if 'error' in region_analysis:
            print(f"❌ Analysis failed: {region_analysis['error']}")
            return False
        
        print(f"✅ Analysis completed successfully!")
        
        # Generate report
        report = generate_region_report(region_analysis)
        
        # Display key results
        print(f"\n" + "=" * 50)
        print("ANALYSIS RESULTS")
        print("=" * 50)
        
        affected_regions = region_analysis.get('affected_regions', {})
        affected_groups = region_analysis.get('affected_groups', {})
        total_volume = region_analysis.get('total_lesion_volume_mm3', 0)
        
        print(f"Total lesion volume: {total_volume:.2f} mm³")
        print(f"Regions affected: {len(affected_regions)}")
        print(f"Anatomical groups affected: {len(affected_groups)}")
        
        if affected_groups:
            print(f"\nAffected groups:")
            for group_name, group_info in affected_groups.items():
                volume = group_info['total_volume_mm3']
                percentage = (volume / total_volume) * 100 if total_volume > 0 else 0
                print(f"  - {group_name}: {volume:.2f} mm³ ({percentage:.1f}%)")
        
        # Save full report
        with open("test_resampling_results.txt", "w", encoding="utf-8") as f:
            f.write("Test Resampling Analysis Results\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Original dimensions:\n")
            f.write(f"  Template: {template_shape}\n")
            f.write(f"  Atlas: {atlas_shape}\n\n")
            f.write(report)
        
        print(f"\n✅ Full report saved to: test_resampling_results.txt")
        
        # Clean up
        if os.path.exists(test_lesion_path):
            os.remove(test_lesion_path)
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        return False

def test_resampling_functions():
    """
    Test the resampling functions directly.
    """
    print("\nTesting Resampling Functions...")
    print("=" * 35)
    
    try:
        from brain_region_mapping import resample_atlas_to_lesion, resample_atlas_simple
        
        # Load test images
        template_path = r"D:/Stroke/Stroke/Prediction_Scripts/flask-app/MNI152_T1_1mm.nii"
        atlas_path = "AAL3v1_1mm.nii.gz"
        
        if not os.path.exists(template_path) or not os.path.exists(atlas_path):
            print("❌ Required files not found for direct testing")
            return False
        
        template_img = nib.load(template_path)
        atlas_img = nib.load(atlas_path)
        
        print(f"Testing resampling from {atlas_img.get_fdata().shape} to {template_img.get_fdata().shape}")
        
        # Test nibabel resampling
        try:
            resampled_data = resample_atlas_to_lesion(atlas_img, template_img)
            print(f"✅ Nibabel resampling successful: {resampled_data.shape}")
        except Exception as e:
            print(f"⚠️  Nibabel resampling failed: {e}")
            
            # Test scipy fallback
            try:
                resampled_data = resample_atlas_simple(atlas_img, template_img)
                print(f"✅ Scipy fallback resampling successful: {resampled_data.shape}")
            except Exception as e2:
                print(f"❌ Both resampling methods failed: {e2}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing resampling functions: {e}")
        return False

def main():
    """
    Run all resampling tests.
    """
    print("Resampling Functionality Test Suite")
    print("=" * 40)
    
    # Test 1: Direct resampling functions
    resampling_success = test_resampling_functions()
    
    # Test 2: Full analysis with dimension mismatch resolution
    analysis_success = test_dimension_mismatch_resolution()
    
    print("\n" + "=" * 40)
    print("TEST SUMMARY")
    print("=" * 40)
    print(f"Resampling Functions: {'✅ PASSED' if resampling_success else '❌ FAILED'}")
    print(f"Full Analysis Pipeline: {'✅ PASSED' if analysis_success else '❌ FAILED'}")
    
    if resampling_success and analysis_success:
        print("\n🎉 All tests PASSED!")
        print("\nDimension mismatch issue has been resolved!")
        print("The system can now handle different atlas and lesion dimensions.")
        print("\nKey features:")
        print("• Automatic atlas resampling to match lesion dimensions")
        print("• Fallback resampling methods for robustness")
        print("• Preserved anatomical labels during resampling")
        print("• Enhanced parietal and SMA analysis")
    else:
        print("\n❌ Some tests FAILED. Please check the error messages above.")
        
    return resampling_success and analysis_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
