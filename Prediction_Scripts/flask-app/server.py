# 服务器端 Flask 应用程序

from flask import Flask, request, send_file
import os
import tempfile
import subprocess
import logging
from logging.handlers import RotatingFileHandler
from nnunetv2.paths import nnUNet_results, nnUNet_raw
import torch
from batchgenerators.utilities.file_and_folder_operations import join
from nnunetv2.inference.predict_from_raw_data import nnUNetPredictor
import ants
import nibabel as nib
import glob
import zipfile
import atexit
import numpy as np
import math
from brain_region_mapping import analyze_lesion_regions, generate_region_report, generate_report_with_pdf, PDF_AVAILABLE

nnUNet_results = r"D:/Stroke/Stroke/Trained_Models"

def run_command(command):
    try:
        result = subprocess.run(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)
        if result.returncode != 0:
            print("Command failed with return code", result.returncode)
        return result.stdout
    except Exception as e:
        print("Exception occurred while running command:", str(e))
        return None

# 配准到模板空间
def coregister_to_template_space(filename,template_path,output_dir):
    image = ants.image_read(filename)
    file_path, ori_name = os.path.split(filename)
    base_name, extension = os.path.splitext(ori_name)
    imagedenoise = ants.denoise_image(image, ants.get_mask(image))
    image_n4 = ants.n4_bias_field_correction(imagedenoise)
    template = ants.image_read(template_path)
    out = ants.registration(template, image_n4, type_of_transform='SyN')
    reg_img = out['warpedmovout']
    output_name=base_name + extension
    ants.image_write(reg_img, os.path.join(output_dir,output_name))
# 去除颅骨
def strip_skull(file_path, mask_path):
    mask_data = mask_path.get_fdata()
    img = nib.load(file_path)
    img_data = img.get_fdata()
    img_data[mask_data != 1] = 0
    output_img = nib.Nifti1Image(img_data, img.affine)
    nib.save(output_img, file_path)
# 预测
def predict_function(temp_dir, filename, modal):
    # instantiate the nnUNetPredictor
    predictor = nnUNetPredictor(
        tile_step_size=0.5,
        use_gaussian=True,
        use_mirroring=True,
        perform_everything_on_device=True,
        device=torch.device('cuda', 0),
        verbose=False,
        verbose_preprocessing=False,
        allow_tqdm=True
    )
    # initializes the network architecture, loads the checkpoint
    if modal == 'DWI':
        print("Using model: DWI")
        predict_model = 'Dataset003_DWI/nnUNetTrainer__nnUNetPlans__3d_fullres'
    else:
        print("Using model: T1")
        predict_model = 'Dataset006_ATLAS/nnUNetTrainer__nnUNetPlans__3d_fullres'
    predictor.initialize_from_trained_model_folder(
        join(nnUNet_results, predict_model),
        use_folds=('all'),  #0,1,2,3,4,'all'
        checkpoint_name='checkpoint_best.pth',
    )
    # variant 1: give input and output folders
    predictor.predict_from_files([[os.path.join(temp_dir, filename)]],
                                [os.path.join(temp_dir, 'mask_'+filename)],
                                save_probabilities=True, overwrite=False,
                                num_processes_preprocessing=16, num_processes_segmentation_export=2,
                                folder_with_segs_from_prev_stage=None, num_parts=1, part_id=0)
    return os.path.join(temp_dir, 'mask_'+filename)


def calculate_volume(mask_file_path):
    """
    Calculate the volume of the stroke region in cubic millimeters.

    :param mask_file_path: Path to the segmentation mask file.
    :return: Volume in cubic millimeters.
    """
    # Load the mask file
    mask_img = nib.load(mask_file_path)
    mask_data = mask_img.get_fdata()

    # Get voxel dimensions in mm
    voxel_dims = mask_img.header.get_zooms()
    voxel_volume = voxel_dims[0] * voxel_dims[1] * voxel_dims[2]  # in cubic mm

    # Count voxels in the stroke region (typically labeled as 1)
    stroke_voxel_count = np.sum(mask_data > 0.5)  # Using 0.5 as threshold for binary mask

    # Calculate volume in cubic mm
    volume_mm3 = stroke_voxel_count * voxel_volume

    return volume_mm3


def analyze_stroke_regions(mask_file_path, template_path, patient_info=None, generate_pdf=True):
    """
    Analyze which brain regions are affected by the stroke lesion.

    :param mask_file_path: Path to the segmentation mask file.
    :param template_path: Path to the MNI152 template file.
    :param patient_info: Optional patient information dictionary.
    :param generate_pdf: Whether to generate PDF report (default: True).
    :return: Dictionary containing region analysis and formatted reports.
    """
    try:
        # Get the AAL atlas path (1mm resolution version)
        script_dir = os.path.dirname(os.path.abspath(__file__))
        aal_atlas_path = os.path.join(script_dir, 'AAL3v1_1mm.nii.gz')

        # Perform region analysis with AAL3 1mm atlas
        region_analysis = analyze_lesion_regions(
            lesion_mask_path=mask_file_path,
            atlas_path=aal_atlas_path,  # Use the real AAL3 1mm atlas
            template_path=template_path
        )

        if generate_pdf and PDF_AVAILABLE:
            # Generate both text and PDF reports
            output_dir = os.path.dirname(mask_file_path)
            reports = generate_report_with_pdf(region_analysis, output_dir, patient_info)

            return {
                'analysis': region_analysis,
                'text_report': reports['text_content'],
                'text_report_path': reports['text_report_path'],
                'pdf_report_path': reports['pdf_report_path'],
                'pdf_available': reports['pdf_report_path'] is not None,
                'success': True
            }
        else:
            # Generate only text report
            region_report = generate_region_report(region_analysis)

            return {
                'analysis': region_analysis,
                'text_report': region_report,
                'text_report_path': None,
                'pdf_report_path': None,
                'pdf_available': False,
                'success': True
            }

    except Exception as e:
        return {
            'analysis': None,
            'text_report': f"Error in brain region analysis: {str(e)}",
            'text_report_path': None,
            'pdf_report_path': None,
            'pdf_available': False,
            'success': False
        }

def zip_nii_gz_files(directory_path, output_zip_path, volume_info=None, original_filename=None, region_info=None):
    """
    将指定目录下的所有.nii.gz文件压缩成一个zip文件。

    :param directory_path: 查找.nii.gz文件的目录路径。
    :param output_zip_path: 输出的zip文件的完整路径。
    :param volume_info: Optional volume information to include in the zip file.
    :param original_filename: Original filename to include in the volume information.
    :param region_info: Optional brain region analysis information to include.
    """
    # 查找目录下的所有.nii.gz文件
    nii_gz_files = glob.glob(os.path.join(directory_path, '*.nii.gz')) + glob.glob(os.path.join(directory_path, '*.nii'))

    # If volume information is provided, create a text file with the information
    if volume_info is not None:
        # Include the original filename in the volume info file name if provided
        if original_filename:
            base_name = os.path.splitext(os.path.basename(original_filename))[0]
            volume_file_path = os.path.join(directory_path, f'stroke_analysis_{base_name}.txt')
        else:
            volume_file_path = os.path.join(directory_path, 'stroke_analysis.txt')

        with open(volume_file_path, 'w', encoding='utf-8') as f:
            if original_filename:
                f.write(f"File: {original_filename}\n")
                f.write("=" * 50 + "\n\n")

            f.write(f"Stroke Region Volume: {volume_info:.2f} cubic mm\n\n")

            # Add brain region information if available
            if region_info and region_info.get('success', False):
                f.write("BRAIN REGION ANALYSIS\n")
                f.write("=" * 50 + "\n\n")
                f.write(region_info['report'])
            elif region_info and not region_info.get('success', False):
                f.write("BRAIN REGION ANALYSIS\n")
                f.write("=" * 50 + "\n\n")
                f.write(region_info['report'])

    # 创建一个zip文件并添加.nii.gz文件
    with zipfile.ZipFile(output_zip_path, 'w') as zipf:
        for file in nii_gz_files:
            zipf.write(file, os.path.basename(file))

        # Add analysis file if it exists
        if volume_info is not None:
            zipf.write(volume_file_path, os.path.basename(volume_file_path))

    #print(f"Zip文件已创建：{output_zip_path}")

app = Flask(__name__)

handler = RotatingFileHandler('app.log', maxBytes=10000, backupCount=1)
handler.setLevel(logging.DEBUG) # 设置日志级别
# 创建日志格式器，并将其设置给处理器
formatter = logging.Formatter("[%(asctime)s] {%(pathname)s:%(lineno)d} %(levelname)s - %(message)s")
handler.setFormatter(formatter)
# 将处理器添加到Flask应用的logger对象
app.logger.addHandler(handler)
# 设置Flask应用的logger的日志级别为DEBUG
app.logger.setLevel(logging.DEBUG)

def close_log_handler():
    handler.close()
    app.logger.removeHandler(handler)

atexit.register(close_log_handler)

# 允许上传的文件类型
ALLOWED_EXTENSIONS = {'nii', 'nii.gz'}

# 检查文件类型是否合法
def allowed_file(filename):
    return '.' in filename and filename.split('.', 1)[1] in ALLOWED_EXTENSIONS

# 文件上传路由
@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return "No file part"
    file = request.files['file']
    modal = request.form.get('modal', None)
    if file.filename == '':
        return "No selected file"
    if file and allowed_file(file.filename):
        temp_dir = tempfile.mkdtemp()
        file_path = os.path.join(temp_dir, file.filename)
        file.save(file_path)

        # Step 1 Coregister to template space
        print("Coregistering to template space...")
        template_path = r"D:/Stroke/Stroke/Prediction_Scripts/flask-app/MNI152_T1_1mm.nii"
        coregister_to_template_space(file_path, template_path, temp_dir)

        # Step 2 Strip the skull
        print("Stripping the skull...")
        mask_path = nib.load(r"D:/Stroke/Stroke/Prediction_Scripts/flask-app/MNI152_T1_1mm_brain_mask.nii")
        strip_skull(file_path, mask_path)

        # Step 3 Predict
        print("Predicting...")
        result_file_path = predict_function(temp_dir, file.filename, modal)

        # Step 4 Calculate volume
        print("Calculating stroke volume...")
        stroke_volume = calculate_volume(result_file_path)
        print(f"Stroke volume: {stroke_volume:.2f} cubic mm")

        # Step 5 Analyze brain regions
        print("Analyzing affected brain regions...")
        region_analysis = analyze_stroke_regions(result_file_path, template_path)
        if region_analysis['success']:
            print("Brain region analysis completed successfully")
        else:
            print(f"Brain region analysis failed: {region_analysis['report']}")

        # Step 6 Save results
        print('Saving...')
        zip_nii_gz_files(temp_dir, os.path.join(temp_dir, f'{str(file.filename)}.zip'),
                         volume_info=stroke_volume,
                         original_filename=file.filename,
                         region_info=region_analysis)
        result_zip_path = os.path.join(temp_dir, f'{str(file.filename)}.zip')

        return send_file(result_zip_path, as_attachment=True)
    else:
        return "Invalid file type"

if __name__ == '__main__':
    app.run()
