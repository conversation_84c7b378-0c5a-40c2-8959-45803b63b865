"""
Simple verification script for 1mm AAL atlas integration.
"""

import os
import sys

def verify_files_exist():
    """Verify required files exist."""
    print("Verifying Required Files...")
    print("=" * 30)
    
    required_files = [
        ("AAL3v1_1mm.nii.gz", "AAL3 1mm atlas file"),
        ("AAL3v1_1mm.nii.txt", "AAL3 1mm labels file"),
        ("MNI152_T1_1mm.nii", "MNI152 1mm template"),
        ("brain_region_mapping.py", "Updated mapping module"),
        ("server.py", "Updated server"),
    ]
    
    all_exist = True
    for filename, description in required_files:
        if os.path.exists(filename):
            if filename.endswith('.gz') or filename.endswith('.nii'):
                try:
                    size_mb = os.path.getsize(filename) / (1024 * 1024)
                    print(f"✅ {description} exists ({size_mb:.1f} MB)")
                except:
                    print(f"✅ {description} exists")
            else:
                print(f"✅ {description} exists")
        else:
            print(f"❌ {description} missing: {filename}")
            all_exist = False
    
    return all_exist

def verify_code_updates():
    """Verify code has been updated for 1mm atlas."""
    print("\nVerifying Code Updates...")
    print("=" * 25)
    
    # Check brain_region_mapping.py
    try:
        with open("brain_region_mapping.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        checks = [
            ("AAL3v1_1mm.nii.gz", "1mm atlas path in brain_region_mapping.py"),
            ("atlas_used", "Atlas tracking in results"),
            ("STROKE LESION - BRAIN REGION ANALYSIS REPORT", "Enhanced report header"),
            ("KEY CLINICAL FINDINGS", "Clinical findings section"),
        ]
        
        mapping_ok = True
        for check_text, description in checks:
            if check_text in content:
                print(f"✅ {description}")
            else:
                print(f"❌ {description} missing")
                mapping_ok = False
                
    except Exception as e:
        print(f"❌ Error checking brain_region_mapping.py: {e}")
        mapping_ok = False
    
    # Check server.py
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        if "AAL3v1_1mm.nii.gz" in content:
            print(f"✅ Server updated for 1mm atlas")
            server_ok = True
        else:
            print(f"❌ Server not updated for 1mm atlas")
            server_ok = False
            
    except Exception as e:
        print(f"❌ Error checking server.py: {e}")
        server_ok = False
    
    return mapping_ok and server_ok

def test_basic_import():
    """Test basic import functionality."""
    print("\nTesting Basic Import...")
    print("=" * 20)
    
    try:
        # Test importing the module
        sys.path.insert(0, os.getcwd())
        from brain_region_mapping import AAL_REGIONS, REGION_GROUPS, analyze_lesion_regions
        
        print(f"✅ Successfully imported brain_region_mapping")
        print(f"✅ AAL_REGIONS contains {len(AAL_REGIONS)} regions")
        print(f"✅ REGION_GROUPS contains {len(REGION_GROUPS)} groups")
        
        # Check key regions
        key_regions = [15, 16, 63, 64, 65, 66, 67, 68, 69, 70]  # SMA and parietal
        missing_regions = [r for r in key_regions if r not in AAL_REGIONS]
        
        if not missing_regions:
            print(f"✅ All key regions (SMA, parietal) are defined")
        else:
            print(f"❌ Missing key regions: {missing_regions}")
            return False
        
        # Check key groups
        key_groups = ["Supplementary Motor Area", "Parietal Lobe - Superior", "Parietal Lobe - Inferior"]
        missing_groups = [g for g in key_groups if g not in REGION_GROUPS]
        
        if not missing_groups:
            print(f"✅ All key groups are defined")
        else:
            print(f"❌ Missing key groups: {missing_groups}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def check_atlas_dimensions():
    """Check if atlas dimensions are reasonable for 1mm."""
    print("\nChecking Atlas Dimensions...")
    print("=" * 25)
    
    try:
        import nibabel as nib
        
        # Check template
        if os.path.exists("MNI152_T1_1mm.nii"):
            template_img = nib.load("MNI152_T1_1mm.nii")
            template_shape = template_img.get_fdata().shape
            template_voxel = template_img.header.get_zooms()
            print(f"✅ Template: {template_shape}, voxel: {template_voxel}")
        else:
            print(f"❌ Template not found")
            return False
        
        # Check atlas
        if os.path.exists("AAL3v1_1mm.nii.gz"):
            atlas_img = nib.load("AAL3v1_1mm.nii.gz")
            atlas_shape = atlas_img.get_fdata().shape
            atlas_voxel = atlas_img.header.get_zooms()
            print(f"✅ Atlas: {atlas_shape}, voxel: {atlas_voxel}")
            
            # Check if dimensions match
            if template_shape == atlas_shape:
                print(f"✅ Dimensions match - no more dimension mismatch!")
                return True
            else:
                print(f"❌ Dimensions still don't match: {template_shape} vs {atlas_shape}")
                return False
        else:
            print(f"❌ 1mm Atlas not found")
            return False
            
    except Exception as e:
        print(f"❌ Error checking dimensions: {e}")
        return False

def main():
    """Run all verification checks."""
    print("AAL3 1mm Atlas Integration Verification")
    print("=" * 45)
    
    print("\n1. Checking required files...")
    files_ok = verify_files_exist()
    
    print("\n2. Checking code updates...")
    code_ok = verify_code_updates()
    
    print("\n3. Testing basic import...")
    import_ok = test_basic_import()
    
    print("\n4. Checking atlas dimensions...")
    dimensions_ok = check_atlas_dimensions()
    
    print("\n" + "=" * 45)
    print("VERIFICATION SUMMARY")
    print("=" * 45)
    
    checks = [
        ("Required Files", files_ok),
        ("Code Updates", code_ok),
        ("Basic Import", import_ok),
        ("Atlas Dimensions", dimensions_ok),
    ]
    
    all_passed = True
    for check_name, passed in checks:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{check_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 45)
    if all_passed:
        print("🎉 ALL CHECKS PASSED!")
        print("\nThe dimension mismatch issue has been resolved!")
        print("Your AAL3 1mm integration is ready to use!")
        print("\nKey improvements:")
        print("• 1mm resolution AAL3 atlas (matches MNI152 template)")
        print("• No more dimension mismatch errors")
        print("• Enhanced parietal lobe analysis (4 subdivisions)")
        print("• Dedicated supplementary motor area tracking")
        print("• Comprehensive clinical interpretation")
        
        print("\nYou can now run your stroke analysis without dimension errors!")
    else:
        print("❌ SOME CHECKS FAILED")
        print("Please review the failed checks above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
